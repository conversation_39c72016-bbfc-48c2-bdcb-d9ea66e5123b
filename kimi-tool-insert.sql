-- Kimi 工具配置 SQL INSERT 语句
-- 基于您提供的官方 API 信息

INSERT INTO ai_tool (
    tenant_id,
    name,
    tool_key,
    version,
    api_url,
    api_key,
    auth_type,
    path,
    status,
    weight,
    max_concurrent_calls,
    is_model,
    model_category,
    model_provider,
    remark,
    metadata,
    created_by,
    created_at,
    updated_by,
    updated_at,
    is_deleted
) VALUES (
    1,                                                              -- tenant_id: 租户ID，假设为1
    'Kimi AI模型',                                                   -- name: 工具名称
    'kimi',                                                         -- tool_key: 工具关键字，匹配KimiInvoker中的getToolKey()
    1,                                                              -- version: 版本号
    'https://ark.cn-beijing.volces.com/api/v3',                   -- api_url: API基础地址
    'sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ',        -- api_key: 您提供的API密钥
    'Bearer',                                                       -- auth_type: 认证类型
    '/chat/completions',                                           -- path: API路径
    'AVAILABLE',                                                   -- status: 工具状态
    100,                                                           -- weight: 路由权重
    5,                                                             -- max_concurrent_calls: 并发许可数
    true,                                                          -- is_model: 是否为模型类型
    'TEXT_GENERATION',                                             -- model_category: 模型分类
    'ByteDance',                                                   -- model_provider: 模型提供商（字节跳动）
    'Kimi AI 大语言模型，基于字节跳动火山引擎豆包API，支持文本生成和多模态输入', -- remark: 备注信息
    '{"model":"doubao-seed-1-6-250615","temperature":0.7,"max_tokens":2048,"supports_images":true}', -- metadata: 扩展元数据，包含默认模型名称
    'system',                                                      -- created_by: 创建者
    NOW(),                                                         -- created_at: 创建时间
    'system',                                                      -- updated_by: 更新者
    NOW(),                                                         -- updated_at: 更新时间
    false                                                          -- is_deleted: 软删除标志
);

-- 验证插入结果的查询语句
SELECT 
    id,
    name,
    tool_key,
    api_url,
    model_provider,
    model_category,
    status,
    created_at
FROM ai_tool 
WHERE tool_key = 'kimi' 
  AND is_deleted = false;
