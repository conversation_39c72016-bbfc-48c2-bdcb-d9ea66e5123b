{"tenantId": 1, "name": "<PERSON>i智能助手", "toolKey": "kimi", "version": 1, "apiUrl": "https://ark.cn-beijing.volces.com/api/v3", "apiKey": "sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ", "authType": "Bearer", "path": "/chat/completions", "status": "AVAILABLE", "weight": 100, "maxConcurrentCalls": 10, "isModel": true, "modelCategory": "LLM", "modelProvider": "字节跳动火山引擎", "remark": "基于火山引擎的 Kimi 大语言模型，支持多轮对话和复杂推理", "metadata": "{\"model\":\"doubao-seed-1-6-250615\",\"supports_vision\":true,\"supports_streaming\":true}", "createdBy": "system", "updatedBy": "system", "isDeleted": false}