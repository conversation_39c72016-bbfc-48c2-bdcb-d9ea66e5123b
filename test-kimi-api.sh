#!/bin/bash

# 测试 Kimi API 连接的脚本
# 用于验证 API Key 和端点是否正确配置

API_URL="https://ark.cn-beijing.volces.com/api/v3/chat/completions"
API_KEY="sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ"

echo "测试 Kimi API 连接..."
echo "API URL: $API_URL"
echo "API Key: ${API_KEY:0:10}..."
echo ""

curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "model": "doubao-seed-1-6-250615",
    "messages": [
      {
        "role": "user",
        "content": "你好，请简单介绍一下你自己。"
      }
    ],
    "max_tokens": 100,
    "temperature": 0.7
  }' \
  -w "\n\nHTTP Status: %{http_code}\n" \
  -s

echo ""
echo "如果返回 401，说明 API Key 有问题"
echo "如果返回 200 并有正常响应，说明配置正确"
