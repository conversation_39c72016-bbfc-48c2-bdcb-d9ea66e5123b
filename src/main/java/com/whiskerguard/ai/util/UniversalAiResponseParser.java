/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：UniversalAiResponseParser.java
 * 包    名：com.whiskerguard.ai.util
 * 描    述：通用AI响应解析器，兼容各种大模型的返回格式
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/18
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 通用AI响应解析器
 * <p>
 * 设计目标：
 * 1. 兼容各种大模型的返回格式（GPT、Claude、Kimi、豆包、ChatLaw等）
 * 2. 支持多种字段名称映射（中英文、不同命名风格）
 * 3. 容错性强，即使部分字段解析失败也能返回有用结果
 * 4. 支持嵌套结构和扁平结构
 * 5. 智能类型转换和数据清洗
 */
@Component
public class UniversalAiResponseParser {

    private static final Logger log = LoggerFactory.getLogger(UniversalAiResponseParser.class);
    private final ObjectMapper objectMapper;

    // 字段名称映射表 - 支持多种命名风格
    private static final Map<String, String[]> FIELD_MAPPINGS = new HashMap<>();
    
    static {
        // 风险等级字段映射
        FIELD_MAPPINGS.put("riskLevel", new String[]{
            "整体风险等级评估", "整体风险等级", "风险等级", "风险级别", "总体风险等级",
            "overallRiskLevel", "riskLevel", "risk_level", "overall_risk", "riskGrade"
        });
        
        // 风险评分字段映射
        FIELD_MAPPINGS.put("riskScore", new String[]{
            "风险评分", "风险分数", "总体风险分数", "风险得分", "评分",
            "riskScore", "risk_score", "score", "rating", "riskRating"
        });
        
        // 风险总结字段映射
        FIELD_MAPPINGS.put("riskSummary", new String[]{
            "风险总结", "风险摘要", "总结", "摘要", "概述", "风险概述",
            "riskSummary", "risk_summary", "summary", "overview", "abstract"
        });
        
        // 风险点列表字段映射
        FIELD_MAPPINGS.put("riskPoints", new String[]{
            "具体风险点列表", "风险点列表", "风险点", "具体风险点", "风险清单", "风险项目",
            "riskPoints", "risk_points", "risks", "riskList", "riskItems", "issues"
        });
        
        // 条款问题分析字段映射
        FIELD_MAPPINGS.put("clauseAnalysis", new String[]{
            "条款问题分析", "条款分析", "条款问题", "合同条款分析", "条款风险分析",
            "clauseAnalysis", "clause_analysis", "clauseIssues", "clause_issues", "contractClauses"
        });
        
        // 修改建议字段映射
        FIELD_MAPPINGS.put("recommendations", new String[]{
            "修改建议", "建议", "改进建议", "优化建议", "修改意见", "改进意见",
            "recommendations", "suggestions", "advice", "improvements", "modifications"
        });
        
        // 法律依据字段映射
        FIELD_MAPPINGS.put("legalBasis", new String[]{
            "法律依据说明", "法律依据", "法律条文", "相关法律", "法规依据",
            "legalBasis", "legal_basis", "legalReference", "lawReference", "regulations"
        });
    }

    public UniversalAiResponseParser(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 通用解析方法 - 解析合同审查响应
     */
    public ContractReviewResponseDTO parseContractReviewResponse(String aiResponse) {
        try {
            // 1. 清理和预处理响应
            String cleanedResponse = cleanAndPreprocessResponse(aiResponse);
            log.debug("清理后的响应: {}", cleanedResponse);
            
            // 2. 尝试解析为JSON
            Map<String, Object> responseMap = parseToMap(cleanedResponse);
            if (responseMap == null) {
                return createFallbackResponse(aiResponse, "JSON解析失败");
            }
            
            // 3. 使用通用解析器构建响应
            return buildContractReviewResponse(responseMap, aiResponse);
            
        } catch (Exception e) {
            log.error("通用解析失败", e);
            return createFallbackResponse(aiResponse, "解析异常: " + e.getMessage());
        }
    }

    /**
     * 清理和预处理AI响应
     */
    private String cleanAndPreprocessResponse(String aiResponse) {
        if (aiResponse == null || aiResponse.trim().isEmpty()) {
            return "{}";
        }

        String cleaned = aiResponse;
        
        // 移除各种markdown标记
        cleaned = cleaned.replaceAll("```(?:json|JSON)?\\s*", "");
        cleaned = cleaned.replaceAll("```\\s*", "");
        
        // 移除可能的前导说明文字
        Pattern jsonStartPattern = Pattern.compile("\\{.*", Pattern.DOTALL);
        Matcher matcher = jsonStartPattern.matcher(cleaned);
        if (matcher.find()) {
            cleaned = matcher.group();
        }
        
        // 确保JSON完整性
        int firstBrace = cleaned.indexOf('{');
        int lastBrace = cleaned.lastIndexOf('}');
        if (firstBrace >= 0 && lastBrace > firstBrace) {
            cleaned = cleaned.substring(firstBrace, lastBrace + 1);
        }
        
        return cleaned.trim();
    }

    /**
     * 尝试解析为Map，支持多种格式
     */
    private Map<String, Object> parseToMap(String jsonString) {
        try {
            return objectMapper.readValue(jsonString, Map.class);
        } catch (Exception e) {
            log.warn("标准JSON解析失败，尝试修复: {}", e.getMessage());
            
            // 尝试修复常见的JSON格式问题
            String fixedJson = fixCommonJsonIssues(jsonString);
            try {
                return objectMapper.readValue(fixedJson, Map.class);
            } catch (Exception e2) {
                log.error("JSON修复后仍然解析失败", e2);
                return null;
            }
        }
    }

    /**
     * 修复常见的JSON格式问题
     */
    private String fixCommonJsonIssues(String jsonString) {
        String fixed = jsonString;
        
        // 修复单引号问题
        fixed = fixed.replaceAll("'([^']*)':", "\"$1\":");
        fixed = fixed.replaceAll(":[ ]*'([^']*)'", ": \"$1\"");
        
        // 修复尾随逗号问题
        fixed = fixed.replaceAll(",[ ]*}", "}");
        fixed = fixed.replaceAll(",[ ]*]", "]");
        
        // 修复缺少引号的键名
        fixed = fixed.replaceAll("([{,]\\s*)([a-zA-Z_][a-zA-Z0-9_]*):", "$1\"$2\":");
        
        return fixed;
    }

    /**
     * 构建合同审查响应对象
     */
    private ContractReviewResponseDTO buildContractReviewResponse(Map<String, Object> responseMap, String originalResponse) {
        ContractReviewResponseDTO response = new ContractReviewResponseDTO();
        
        try {
            // 解析基本字段
            response.setOverallRiskLevel(parseRiskLevel(responseMap));
            response.setRiskScore(parseRiskScore(responseMap));
            response.setRiskSummary(parseRiskSummary(responseMap));
            
            // 解析复杂字段
            response.setRiskPoints(parseRiskPoints(responseMap));
            response.setClauseIssues(parseClauseIssues(responseMap));
            response.setRecommendations(parseRecommendations(responseMap));
            
            // 设置默认值
            response.setReviewStatus("COMPLETED");
            response.setConfidence(calculateConfidence(response));
            response.setAiModelInfo("universal-parser");
            
            return response;
            
        } catch (Exception e) {
            log.error("构建响应对象失败", e);
            return createFallbackResponse(originalResponse, "构建响应失败: " + e.getMessage());
        }
    }

    /**
     * 通用字段值获取方法
     */
    private Object getFieldValue(Map<String, Object> map, String fieldKey) {
        String[] possibleKeys = FIELD_MAPPINGS.get(fieldKey);
        if (possibleKeys == null) {
            return map.get(fieldKey);
        }
        
        for (String key : possibleKeys) {
            Object value = map.get(key);
            if (value != null) {
                log.debug("找到字段 {} 使用键名: {}", fieldKey, key);
                return value;
            }
        }
        
        return null;
    }

    /**
     * 解析风险等级
     */
    private RiskLevel parseRiskLevel(Map<String, Object> map) {
        Object value = getFieldValue(map, "riskLevel");
        if (value == null) return RiskLevel.MEDIUM;
        
        String riskStr = value.toString().trim();
        
        // 中文映射
        switch (riskStr) {
            case "低": case "低风险": case "低等": case "LOW":
                return RiskLevel.LOW;
            case "高": case "高风险": case "高等": case "HIGH":
                return RiskLevel.HIGH;
            default:
                return RiskLevel.MEDIUM;
        }
    }

    /**
     * 解析风险评分
     */
    private Integer parseRiskScore(Map<String, Object> map) {
        Object value = getFieldValue(map, "riskScore");
        if (value == null) return 50;
        
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return 50;
        }
    }

    /**
     * 解析风险总结
     */
    private String parseRiskSummary(Map<String, Object> map) {
        Object value = getFieldValue(map, "riskSummary");
        return value != null ? value.toString() : "未提供风险总结";
    }

    /**
     * 解析风险点列表
     */
    @SuppressWarnings("unchecked")
    private List<ContractReviewResponseDTO.RiskPointDTO> parseRiskPoints(Map<String, Object> map) {
        Object value = getFieldValue(map, "riskPoints");
        List<ContractReviewResponseDTO.RiskPointDTO> riskPoints = new ArrayList<>();
        
        if (value instanceof List) {
            List<Object> riskList = (List<Object>) value;
            for (Object riskItem : riskList) {
                ContractReviewResponseDTO.RiskPointDTO riskPoint = new ContractReviewResponseDTO.RiskPointDTO();
                
                if (riskItem instanceof Map) {
                    Map<String, Object> riskMap = (Map<String, Object>) riskItem;
                    riskPoint.setDescription(getStringValue(riskMap, "description", "描述", "风险点", "问题"));
                    riskPoint.setCategory(getStringValue(riskMap, "category", "类别", "分类"));
                    riskPoint.setSeverity(parseRiskLevel(riskMap));
                } else {
                    riskPoint.setDescription(riskItem.toString());
                    riskPoint.setSeverity(RiskLevel.MEDIUM);
                }
                
                riskPoint.setCategory(riskPoint.getCategory() != null ? riskPoint.getCategory() : "合同条款风险");
                riskPoint.setRiskScore(riskPoint.getSeverity() == RiskLevel.HIGH ? 90 : 
                                     riskPoint.getSeverity() == RiskLevel.LOW ? 30 : 60);
                riskPoint.setIsCritical(riskPoint.getSeverity() == RiskLevel.HIGH);
                
                riskPoints.add(riskPoint);
            }
        }
        
        return riskPoints;
    }

    /**
     * 解析条款问题
     */
    @SuppressWarnings("unchecked")
    private List<ContractReviewResponseDTO.ClauseIssueDTO> parseClauseIssues(Map<String, Object> map) {
        Object value = getFieldValue(map, "clauseAnalysis");
        List<ContractReviewResponseDTO.ClauseIssueDTO> clauseIssues = new ArrayList<>();
        
        if (value instanceof Map) {
            Map<String, Object> clauseMap = (Map<String, Object>) value;
            for (Map.Entry<String, Object> entry : clauseMap.entrySet()) {
                if (entry.getValue() instanceof Map) {
                    Map<String, Object> clauseDetail = (Map<String, Object>) entry.getValue();
                    ContractReviewResponseDTO.ClauseIssueDTO issue = new ContractReviewResponseDTO.ClauseIssueDTO();
                    
                    issue.setClauseText(entry.getKey());
                    issue.setDescription(getStringValue(clauseDetail, "problem", "问题", "description"));
                    issue.setLegalRisk(getStringValue(clauseDetail, "risk", "风险", "legalRisk"));
                    issue.setSeverity(RiskLevel.MEDIUM);
                    issue.setIssueType("条款问题");
                    
                    String suggestion = getStringValue(clauseDetail, "suggestion", "建议", "advice");
                    if (suggestion != null) {
                        issue.setSuggestions(Arrays.asList(suggestion));
                    }
                    
                    clauseIssues.add(issue);
                }
            }
        }
        
        return clauseIssues;
    }

    /**
     * 解析建议列表
     */
    @SuppressWarnings("unchecked")
    private List<String> parseRecommendations(Map<String, Object> map) {
        Object value = getFieldValue(map, "recommendations");
        List<String> recommendations = new ArrayList<>();
        
        if (value instanceof List) {
            List<Object> recList = (List<Object>) value;
            for (Object rec : recList) {
                recommendations.add(rec.toString());
            }
        } else if (value instanceof Map) {
            Map<String, Object> recMap = (Map<String, Object>) value;
            for (Map.Entry<String, Object> entry : recMap.entrySet()) {
                recommendations.add(entry.getKey() + ": " + entry.getValue());
            }
        } else if (value != null) {
            recommendations.add(value.toString());
        }
        
        return recommendations;
    }

    /**
     * 通用字符串值获取
     */
    private String getStringValue(Map<String, Object> map, String... keys) {
        for (String key : keys) {
            Object value = map.get(key);
            if (value != null) {
                return value.toString();
            }
        }
        return null;
    }

    /**
     * 计算置信度
     */
    private Integer calculateConfidence(ContractReviewResponseDTO response) {
        int confidence = 70; // 基础置信度
        
        if (response.getRiskPoints() != null && !response.getRiskPoints().isEmpty()) {
            confidence += 10;
        }
        if (response.getClauseIssues() != null && !response.getClauseIssues().isEmpty()) {
            confidence += 10;
        }
        if (response.getRecommendations() != null && !response.getRecommendations().isEmpty()) {
            confidence += 10;
        }
        
        return Math.min(confidence, 95);
    }

    /**
     * 创建降级响应
     */
    private ContractReviewResponseDTO createFallbackResponse(String originalResponse, String reason) {
        ContractReviewResponseDTO response = new ContractReviewResponseDTO();
        response.setOverallRiskLevel(RiskLevel.MEDIUM);
        response.setRiskScore(50);
        response.setRiskSummary("AI分析结果解析失败: " + reason);
        response.setReviewStatus("FAILED");
        response.setConfidence(30);
        
        // 将原始响应作为建议
        List<String> recommendations = new ArrayList<>();
        recommendations.add("AI原始响应：" + originalResponse);
        response.setRecommendations(recommendations);
        
        return response;
    }
}
