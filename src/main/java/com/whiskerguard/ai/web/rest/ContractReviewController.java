/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewController.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：合同智能审查REST API控制器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.service.contract.ContractReviewService;
import com.whiskerguard.ai.service.dto.ContractReviewRequestDTO;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import com.whiskerguard.ai.util.LlmResponseProcessor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 合同智能审查
 * <p>
 * 提供合同智能审查相关的REST API接口，包括：
 * 1. 合同智能审查
 * 2. 流式合同审查
 * 3. 审查历史查询
 * 4. 审查结果导出
 *
 * 所有接口都支持多租户数据隔离。
 */
@RestController
@RequestMapping("/api/ai/contract")
@Tag(name = "合同智能审查", description = "合同智能审查相关API")
public class ContractReviewController {

    private final Logger log = LoggerFactory.getLogger(ContractReviewController.class);

    private final ContractReviewService contractReviewService;

    public ContractReviewController(ContractReviewService contractReviewService) {
        this.contractReviewService = contractReviewService;
    }

    /**
     * JSON 解析测试接口
     * <p>
     * 用于测试 JSON 解析是否正常工作
     */
    @PostMapping("/test-json")
    @Operation(summary = "JSON 解析测试", description = "测试 JSON 解析是否正常工作")
    public ResponseEntity<?> testJsonParsing(
        @Parameter(description = "合同审查请求", required = true) @Valid @RequestBody ContractReviewRequestDTO request
    ) {
        log.info(
            "JSON 解析测试成功 - 租户: {}, 员工: {}, 合同类型: {}",
            request.getTenantId(),
            request.getEmployeeId(),
            request.getContractType()
        );

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "JSON 解析成功");
        response.put(
            "receivedData",
            Map.of(
                "tenantId",
                request.getTenantId(),
                "employeeId",
                request.getEmployeeId(),
                "contractType",
                request.getContractType(),
                "contractContentLength",
                request.getContractContent() != null ? request.getContractContent().length() : 0
            )
        );
        response.put("timestamp", java.time.Instant.now());

        return ResponseEntity.ok(response);
    }

    /**
     * 合同智能审查接口
     * <p>
     * 对合同进行全面的智能审查，包括法律合规性检查、风险评估、
     * 关联方分析等，返回详细的审查报告。
     */
    @PostMapping("/review")
    @Operation(summary = "合同智能审查", description = "对合同进行全面的智能审查，包括法律合规性检查、风险评估、关联方分析等")
    @ApiResponses(
        value = {
            @ApiResponse(
                responseCode = "200",
                description = "审查成功",
                content = @Content(schema = @Schema(implementation = ContractReviewResponseDTO.class))
            ),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<?> reviewContract(
        @Parameter(description = "合同审查请求", required = true) @Valid @RequestBody ContractReviewRequestDTO request
    ) {
        // 处理AI模型选择
        String aiModel = LlmResponseProcessor.normalizeModelName(request.getAiModel());
        request.setAiModel(aiModel);

        log.info(
            "收到合同审查请求 - 租户: {}, 员工: {}, 合同类型: {}, AI模型: {}",
            request.getTenantId(),
            request.getEmployeeId(),
            request.getContractType(),
            aiModel
        );

        try {
            ContractReviewResponseDTO response = contractReviewService.reviewContract(request);

            log.info(
                "合同审查完成 - 审查ID: {}, 风险等级: {}, 耗时: {}ms",
                response.getReviewId(),
                response.getOverallRiskLevel(),
                response.getReviewDuration()
            );

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            log.warn("合同审查请求参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest().body(createErrorResponse("INVALID_ARGUMENT", "请求参数错误: " + e.getMessage()));
        } catch (SecurityException e) {
            log.warn("合同审查权限不足: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(createErrorResponse("ACCESS_DENIED", "权限不足: " + e.getMessage()));
        } catch (Exception e) {
            log.error("合同审查处理失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                createErrorResponse("INTERNAL_ERROR", "服务器内部错误，请稍后重试")
            );
        }
    }

    /**
     * 流式合同审查接口
     * <p>
     * 适用于大型合同的审查，支持实时返回审查进度和中间结果。
     */
    @PostMapping(path = "/review/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流式合同审查", description = "适用于大型合同的审查，支持实时返回审查进度和中间结果")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "流式审查开始"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
        }
    )
    public SseEmitter reviewContractStream(
        @Parameter(description = "合同审查请求", required = true) @Valid @RequestBody ContractReviewRequestDTO request
    ) {
        log.info("收到流式合同审查请求 - 租户: {}, 合同类型: {}", request.getTenantId(), request.getContractType());

        // 延长超时时间，考虑到大型合同处理可能需要更久
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        // 添加完成回调
        emitter.onCompletion(() -> log.debug("流式合同审查完成"));
        emitter.onTimeout(() -> log.warn("流式合同审查超时"));
        emitter.onError(ex -> log.error("流式合同审查出错", ex));

        // 异步处理
        new Thread(() -> {
            boolean completed = false;
            try {
                // 发送开始事件
                sendEvent(emitter, "progress", new ContractReviewStreamEvent("START", "开始合同审查", 0));

                // 发送早期进度事件
                sendEvent(emitter, "progress", new ContractReviewStreamEvent("PROGRESS", "正在提取合同结构", 10));
                Thread.sleep(500);
                sendEvent(emitter, "progress", new ContractReviewStreamEvent("PROGRESS", "正在提取关联方信息", 20));
                Thread.sleep(500);
                sendEvent(emitter, "progress", new ContractReviewStreamEvent("PROGRESS", "正在分析合同条款", 30));
                Thread.sleep(1000);
                sendEvent(emitter, "progress", new ContractReviewStreamEvent("PROGRESS", "正在准备AI处理", 40));
                Thread.sleep(1000);

                // 执行审查 - 增加重试机制
                ContractReviewResponseDTO response = null;
                Exception executionException = null;

                try {
                    // 尝试执行合同审查
                    response = executeWithRetry(() -> contractReviewService.reviewContract(request), 3);

                    // 发送后期进度事件
                    sendEvent(emitter, "progress", new ContractReviewStreamEvent("PROGRESS", "正在评估合规风险", 70));
                    Thread.sleep(500);
                    sendEvent(emitter, "progress", new ContractReviewStreamEvent("PROGRESS", "正在生成最终报告", 90));
                    Thread.sleep(500);

                    // 发送完成事件
                    sendEvent(emitter, "complete", new ContractReviewStreamEvent("COMPLETE", "审查完成", 100, response));
                } catch (Exception e) {
                    log.error("合同审查处理失败", e);
                    executionException = e;

                    // 由于移除了fallbackContractAnalysis方法，直接发送错误事件
                    // 不再尝试备用方案
                    sendEvent(emitter, "error", new ContractReviewStreamEvent("ERROR", formatErrorMessage(e), null));
                    completed = true;
                }

                // 如果仍然有异常，则发送错误事件
                if (executionException != null && !completed) {
                    sendEvent(emitter, "error", new ContractReviewStreamEvent("ERROR", formatErrorMessage(executionException), null));
                }

                // 如果未标记完成，则标记完成
                if (!completed) {
                    completed = true;
                    emitter.complete();
                }
            } catch (Exception e) {
                log.error("流式合同审查失败", e);
                // 只有在emitter还未完成时才发送错误事件
                if (!completed) {
                    try {
                        sendEvent(emitter, "error", new ContractReviewStreamEvent("ERROR", formatErrorMessage(e), null));
                    } catch (Exception sendException) {
                        log.error("发送错误事件失败", sendException);
                    } finally {
                        emitter.completeWithError(e);
                    }
                }
            }
        }).start();

        return emitter;
    }

    /**
     * 格式化用户友好的错误消息
     */
    private String formatErrorMessage(Exception e) {
        String message = e.getMessage();

        // 处理特定类型的错误，返回更友好的消息
        if (message != null && message.contains("Channel shutdown invoked")) {
            return "服务连接暂时不可用，请稍后再试";
        } else if (message != null && message.contains("WORKFLOW_EXECUTION_FAILED")) {
            return "合同分析过程中遇到问题，请稍后重试";
        }

        // 避免暴露完整的技术错误栈给用户
        return "处理请求时发生错误" + (message != null && !message.isEmpty() ? ": " + message.split("\n")[0] : "");
    }

    /**
     * 使用重试机制执行函数
     */
    private <T> T executeWithRetry(Supplier<T> supplier, int maxRetries) throws Exception {
        Exception lastException = null;

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                return supplier.get();
            } catch (Exception e) {
                lastException = e;
                log.warn("执行失败，尝试重试 ({}/{}): {}", attempt + 1, maxRetries, e.getMessage());

                // 检查是否值得重试
                if (!isRetryable(e)) {
                    log.warn("错误不可重试，放弃尝试");
                    throw e;
                }

                // 指数退避
                if (attempt < maxRetries - 1) {
                    Thread.sleep((long) Math.pow(2, attempt) * 1000);
                }
            }
        }

        throw lastException;
    }

    /**
     * 判断异常是否可以重试
     */
    private boolean isRetryable(Exception e) {
        String message = e.getMessage();
        return (
            message != null &&
            (message.contains("Channel shutdown") || message.contains("UNAVAILABLE") || message.contains("CONNECTION_ERROR"))
        );
    }

    /**
     * 安全地发送SSE事件
     *
     * @param emitter SSE发射器
     * @param eventName 事件名称
     * @param data 事件数据
     * @throws IOException 如果发送失败
     */
    private void sendEvent(SseEmitter emitter, String eventName, Object data) throws IOException {
        try {
            emitter.send(SseEmitter.event().name(eventName).data(data));
        } catch (Exception e) {
            log.warn("发送SSE事件失败: {}", e.getMessage());
            throw e; // 重新抛出异常以便上层处理
        }
    }

    /**
     * 获取合同审查历史
     */
    @GetMapping("/review/history")
    @Operation(summary = "获取合同审查历史", description = "分页查询用户的合同审查历史记录")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "查询成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权访问"),
        }
    )
    public ResponseEntity<Page<ContractReviewHistoryDTO>> getReviewHistory(
        @Parameter(description = "租户ID", required = true) @RequestParam Long tenantId,
        @Parameter(description = "员工ID", required = true) @RequestParam Long employeeId,
        @Parameter(description = "分页参数") Pageable pageable
    ) {
        log.debug("查询合同审查历史 - 租户: {}, 员工: {}", tenantId, employeeId);

        try {
            Page<ContractReviewHistoryDTO> history = contractReviewService.getReviewHistory(tenantId, employeeId, pageable);

            return ResponseEntity.ok(history);
        } catch (Exception e) {
            log.error("查询审查历史失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取审查详情
     */
    @GetMapping("/review/{reviewId}")
    @Operation(summary = "获取审查详情", description = "根据审查ID获取详细的审查结果")
    public ResponseEntity<ContractReviewResponseDTO> getReviewDetail(
        @Parameter(description = "审查ID", required = true) @PathVariable Long reviewId,
        @Parameter(description = "租户ID", required = true) @RequestParam Long tenantId
    ) {
        log.debug("获取审查详情 - 审查ID: {}, 租户: {}", reviewId, tenantId);

        try {
            ContractReviewResponseDTO detail = contractReviewService.getReviewDetail(reviewId, tenantId);

            if (detail == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(detail);
        } catch (SecurityException e) {
            log.warn("获取审查详情权限不足: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        } catch (Exception e) {
            log.error("获取审查详情失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 导出审查报告
     */
    @GetMapping("/review/{reviewId}/export")
    @Operation(summary = "导出审查报告", description = "导出合同审查报告为PDF或Word格式")
    public ResponseEntity<byte[]> exportReviewReport(
        @Parameter(description = "审查ID", required = true) @PathVariable Long reviewId,
        @Parameter(description = "租户ID", required = true) @RequestParam Long tenantId,
        @Parameter(description = "导出格式", example = "PDF") @RequestParam(defaultValue = "PDF") String format
    ) {
        log.info("导出审查报告 - 审查ID: {}, 租户: {}, 格式: {}", reviewId, tenantId, format);

        try {
            byte[] reportData = contractReviewService.exportReviewReport(reviewId, tenantId, format);

            String contentType = "PDF".equalsIgnoreCase(format)
                ? "application/pdf"
                : "application/vnd.openxmlformats-officedocument.wordprocessingml.document";

            String filename = String.format("contract_review_%d.%s", reviewId, format.toLowerCase());

            return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header("Content-Disposition", "attachment; filename=" + filename)
                .body(reportData);
        } catch (Exception e) {
            log.error("导出审查报告失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取审查统计信息
     */
    @GetMapping("/review/statistics")
    @Operation(summary = "获取审查统计信息", description = "获取租户的合同审查统计数据")
    public ResponseEntity<Map<String, Object>> getReviewStatistics(
        @Parameter(description = "租户ID", required = true) @RequestParam Long tenantId,
        @Parameter(description = "统计时间范围（天）", example = "30") @RequestParam(defaultValue = "30") Integer days
    ) {
        log.debug("获取审查统计信息 - 租户: {}, 天数: {}", tenantId, days);

        try {
            Map<String, Object> statistics = contractReviewService.getReviewStatistics(tenantId, days);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取审查统计信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 流式事件数据类
     */
    public static class ContractReviewStreamEvent {

        private String type;
        private String message;
        private Integer progress;
        private ContractReviewResponseDTO data;

        public ContractReviewStreamEvent(String type, String message, Integer progress) {
            this(type, message, progress, null);
        }

        public ContractReviewStreamEvent(String type, String message, Integer progress, ContractReviewResponseDTO data) {
            this.type = type;
            this.message = message;
            this.progress = progress;
            this.data = data;
        }

        // Getters and Setters
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getProgress() {
            return progress;
        }

        public void setProgress(Integer progress) {
            this.progress = progress;
        }

        public ContractReviewResponseDTO getData() {
            return data;
        }

        public void setData(ContractReviewResponseDTO data) {
            this.data = data;
        }
    }

    /**
     * 审查历史DTO（简化版本）
     */
    public static class ContractReviewHistoryDTO {

        private Long id;
        private String contractType;
        private String contractTitle;
        private String overallRiskLevel;
        private Integer riskScore;
        private String status;
        private java.time.Instant reviewTime;
        private Long reviewDuration;

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getContractType() {
            return contractType;
        }

        public void setContractType(String contractType) {
            this.contractType = contractType;
        }

        public String getContractTitle() {
            return contractTitle;
        }

        public void setContractTitle(String contractTitle) {
            this.contractTitle = contractTitle;
        }

        public String getOverallRiskLevel() {
            return overallRiskLevel;
        }

        public void setOverallRiskLevel(String overallRiskLevel) {
            this.overallRiskLevel = overallRiskLevel;
        }

        public Integer getRiskScore() {
            return riskScore;
        }

        public void setRiskScore(Integer riskScore) {
            this.riskScore = riskScore;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public java.time.Instant getReviewTime() {
            return reviewTime;
        }

        public void setReviewTime(java.time.Instant reviewTime) {
            this.reviewTime = reviewTime;
        }

        public Long getReviewDuration() {
            return reviewDuration;
        }

        public void setReviewDuration(Long reviewDuration) {
            this.reviewDuration = reviewDuration;
        }
    }

    /**
     * 处理 JSON 解析错误
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<Map<String, Object>> handleJsonParseError(HttpMessageNotReadableException e) {
        log.warn("JSON 解析错误: {}", e.getMessage());

        String errorMessage = "JSON 格式错误";
        if (e.getMessage() != null) {
            if (e.getMessage().contains("Unexpected character")) {
                errorMessage = "JSON 格式错误：包含无效字符，请检查请求体格式和字符编码";
            } else if (e.getMessage().contains("was expecting comma")) {
                errorMessage = "JSON 格式错误：缺少逗号分隔符，请检查 JSON 语法";
            } else if (e.getMessage().contains("Unexpected end-of-input")) {
                errorMessage = "JSON 格式错误：JSON 结构不完整";
            }
        }

        return ResponseEntity.badRequest().body(createErrorResponse("JSON_PARSE_ERROR", errorMessage));
    }

    /**
     * 处理参数验证错误
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, Object>> handleValidationError(MethodArgumentNotValidException e) {
        log.warn("参数验证错误: {}", e.getMessage());

        BindingResult bindingResult = e.getBindingResult();
        Map<String, String> fieldErrors = new HashMap<>();

        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            fieldErrors.put(fieldError.getField(), fieldError.getDefaultMessage());
        }

        Map<String, Object> errorResponse = createErrorResponse("VALIDATION_ERROR", "请求参数验证失败");
        errorResponse.put("fieldErrors", fieldErrors);

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String errorCode, String errorMessage) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", errorCode);
        errorResponse.put("errorMessage", errorMessage);
        errorResponse.put("timestamp", java.time.Instant.now());
        return errorResponse;
    }
}
