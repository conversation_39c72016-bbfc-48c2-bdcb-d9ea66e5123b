/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：LlmResponseProcessor.java
 * 包    名：com.whiskerguard.ai.util
 * 描    述：LLM响应通用处理器，统一处理各种大模型的返回值
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/18
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * LLM响应通用处理器
 * <p>
 * 设计目标：
 * 1. 统一处理各种大模型的返回值格式
 * 2. 支持多种字段名称映射（中英文、不同命名风格）
 * 3. 容错性强，即使部分字段解析失败也能返回有用结果
 * 4. 支持嵌套结构和扁平结构
 * 5. 智能类型转换和数据清洗
 */
@Component
public class LlmResponseProcessor {

    private static final Logger log = LoggerFactory.getLogger(LlmResponseProcessor.class);
    private final ObjectMapper objectMapper;

    public LlmResponseProcessor(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 通用LLM响应处理方法
     * 
     * @param aiResponse 原始AI响应
     * @param expectedFields 期望的字段映射表
     * @return 处理后的结构化数据
     */
    public Map<String, Object> processLlmResponse(String aiResponse, Map<String, String[]> expectedFields) {
        try {
            // 1. 清理和预处理响应
            String cleanedResponse = cleanAndPreprocessResponse(aiResponse);
            log.debug("清理后的响应: {}", cleanedResponse);
            
            // 2. 尝试解析为JSON
            Map<String, Object> responseMap = parseToMap(cleanedResponse);
            if (responseMap == null) {
                return createFallbackResponse(aiResponse, "JSON解析失败");
            }
            
            // 3. 使用字段映射提取数据
            return extractFieldsWithMapping(responseMap, expectedFields);
            
        } catch (Exception e) {
            log.error("LLM响应处理失败", e);
            return createFallbackResponse(aiResponse, "处理异常: " + e.getMessage());
        }
    }

    /**
     * 清理和预处理AI响应
     */
    private String cleanAndPreprocessResponse(String aiResponse) {
        if (aiResponse == null || aiResponse.trim().isEmpty()) {
            return "{}";
        }

        String cleaned = aiResponse;
        
        // 移除各种markdown标记
        cleaned = cleaned.replaceAll("```(?:json|JSON)?\\s*", "");
        cleaned = cleaned.replaceAll("```\\s*", "");
        
        // 移除可能的前导说明文字
        Pattern jsonStartPattern = Pattern.compile("\\{.*", Pattern.DOTALL);
        Matcher matcher = jsonStartPattern.matcher(cleaned);
        if (matcher.find()) {
            cleaned = matcher.group();
        }
        
        // 确保JSON完整性
        int firstBrace = cleaned.indexOf('{');
        int lastBrace = cleaned.lastIndexOf('}');
        if (firstBrace >= 0 && lastBrace > firstBrace) {
            cleaned = cleaned.substring(firstBrace, lastBrace + 1);
        }
        
        return cleaned.trim();
    }

    /**
     * 尝试解析为Map，支持多种格式
     */
    private Map<String, Object> parseToMap(String jsonString) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> result = objectMapper.readValue(jsonString, Map.class);
            return result;
        } catch (Exception e) {
            log.warn("标准JSON解析失败，尝试修复: {}", e.getMessage());
            
            // 尝试修复常见的JSON格式问题
            String fixedJson = fixCommonJsonIssues(jsonString);
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> result = objectMapper.readValue(fixedJson, Map.class);
                return result;
            } catch (Exception e2) {
                log.error("JSON修复后仍然解析失败", e2);
                return null;
            }
        }
    }

    /**
     * 修复常见的JSON格式问题
     */
    private String fixCommonJsonIssues(String jsonString) {
        String fixed = jsonString;
        
        // 修复单引号问题
        fixed = fixed.replaceAll("'([^']*)':", "\"$1\":");
        fixed = fixed.replaceAll(":[ ]*'([^']*)'", ": \"$1\"");
        
        // 修复尾随逗号问题
        fixed = fixed.replaceAll(",[ ]*}", "}");
        fixed = fixed.replaceAll(",[ ]*]", "]");
        
        // 修复缺少引号的键名
        fixed = fixed.replaceAll("([{,]\\s*)([a-zA-Z_][a-zA-Z0-9_]*):", "$1\"$2\":");
        
        return fixed;
    }

    /**
     * 使用字段映射提取数据
     */
    private Map<String, Object> extractFieldsWithMapping(Map<String, Object> responseMap, Map<String, String[]> expectedFields) {
        Map<String, Object> result = new HashMap<>();
        
        for (Map.Entry<String, String[]> entry : expectedFields.entrySet()) {
            String standardFieldName = entry.getKey();
            String[] possibleKeys = entry.getValue();
            
            Object value = getFieldValue(responseMap, possibleKeys);
            if (value != null) {
                result.put(standardFieldName, value);
                log.debug("成功提取字段 {} = {}", standardFieldName, value);
            } else {
                log.debug("未找到字段: {}", standardFieldName);
            }
        }
        
        return result;
    }

    /**
     * 通用字段值获取方法
     */
    private Object getFieldValue(Map<String, Object> map, String[] possibleKeys) {
        for (String key : possibleKeys) {
            Object value = map.get(key);
            if (value != null) {
                log.debug("找到字段值，使用键名: {}", key);
                return value;
            }
        }
        return null;
    }

    /**
     * 获取字符串值（带默认值）
     */
    public String getStringValue(Map<String, Object> map, String[] possibleKeys, String defaultValue) {
        Object value = getFieldValue(map, possibleKeys);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 获取整数值（带默认值）
     */
    public Integer getIntegerValue(Map<String, Object> map, String[] possibleKeys, Integer defaultValue) {
        Object value = getFieldValue(map, possibleKeys);
        if (value == null) return defaultValue;
        
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法解析整数值: {}", value);
            return defaultValue;
        }
    }

    /**
     * 获取列表值
     */
    @SuppressWarnings("unchecked")
    public List<Object> getListValue(Map<String, Object> map, String[] possibleKeys) {
        Object value = getFieldValue(map, possibleKeys);
        if (value instanceof List) {
            return (List<Object>) value;
        }
        return new ArrayList<>();
    }

    /**
     * 获取Map列表值
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getListMapValue(Map<String, Object> map, String[] possibleKeys) {
        Object value = getFieldValue(map, possibleKeys);
        if (value instanceof List) {
            List<Object> list = (List<Object>) value;
            List<Map<String, Object>> result = new ArrayList<>();
            for (Object item : list) {
                if (item instanceof Map) {
                    result.add((Map<String, Object>) item);
                }
            }
            return result;
        }
        return new ArrayList<>();
    }

    /**
     * 获取嵌套Map值
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getMapValue(Map<String, Object> map, String[] possibleKeys) {
        Object value = getFieldValue(map, possibleKeys);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return new HashMap<>();
    }

    /**
     * 创建降级响应
     */
    private Map<String, Object> createFallbackResponse(String originalResponse, String reason) {
        Map<String, Object> fallback = new HashMap<>();
        fallback.put("success", false);
        fallback.put("error", reason);
        fallback.put("originalResponse", originalResponse);
        fallback.put("timestamp", System.currentTimeMillis());
        return fallback;
    }

    /**
     * 获取默认模型名称
     */
    public static String getDefaultModel() {
        return "kimi";
    }

    /**
     * 验证模型名称是否有效
     */
    public static boolean isValidModel(String modelName) {
        if (modelName == null || modelName.trim().isEmpty()) {
            return false;
        }
        
        // 支持的模型列表
        Set<String> supportedModels = Set.of(
            "kimi", "gpt", "claude", "doubao", "deepseek", 
            "chatlaw", "lawgpt", "qwen", "baichuan"
        );
        
        return supportedModels.contains(modelName.toLowerCase());
    }

    /**
     * 标准化模型名称
     */
    public static String normalizeModelName(String modelName) {
        if (modelName == null || modelName.trim().isEmpty()) {
            return getDefaultModel();
        }
        
        String normalized = modelName.toLowerCase().trim();
        return isValidModel(normalized) ? normalized : getDefaultModel();
    }
}
