/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RetrievalServiceClient.java
 * 包    名：com.whiskerguard.ai.client
 * 描    述：猫伯伯合规管家公共微服务：提供通用工具类和帮助模块、云对象存储（COS）服务、分布式配置与安全组件、消息和事件驱动服务、缓存与分布式协同、国际化、多语言支持、公共 API 响应与文档功能
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/5/19
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.client;

import com.whiskerguard.ai.client.dto.DocumentRecordDTO;
import com.whiskerguard.ai.client.dto.IndexRequestDTO;
import com.whiskerguard.ai.client.dto.RetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 检索服务客户端接口
 * <p>
 * 该接口使用 Spring Cloud OpenFeign 实现微服务间的远程调用，
 * 用于与文档检索服务（whiskerguard-retrieval-service）进行通信。
 * 提供文档索引和向量检索功能。
 *
 * 配置说明：
 * - 使用 UserFeignClientInterceptor 自动传递认证信息
 * - 配置了自定义的 Feign 配置类
 */
@FeignClient(name = "whiskerguardretrievalservice", configuration = RetrievalServiceClient.RetrievalServiceClientConfiguration.class)
public interface RetrievalServiceClient {
    /**
     * 索引文档接口
     * <p>
     * 将文档内容提交到检索服务进行向量化处理和索引存储。
     * 文档会被切分为多个文本片段，并转换为向量表示后存入向量数据库。
     *
     * @param tenantId 租户ID，用于多租户隔离
     * @param request  索引请求对象，包含文档ID和文本内容
     * @return 文档记录DTO，包含索引结果和文档元数据
     */
    @PostMapping("/api/index/{tenantId}")
    DocumentRecordDTO indexDocument(@PathVariable("tenantId") String tenantId, @RequestBody IndexRequestDTO request);

    /**
     * 检索接口
     * <p>
     * 根据查询文本，在向量数据库中检索最相似的文本片段。
     * 支持多种相似度计算方式和结果格式化选项。
     *
     * @param tenantId 租户ID，用于多租户隔离
     * @param request  检索请求对象，包含查询文本和检索参数
     * @return 检索响应DTO，包含匹配结果列表和相似度分数
     */
    @PostMapping("/api/retrieve/{tenantId}")
    RetrieveResponseDTO retrieve(@PathVariable("tenantId") String tenantId, @RequestBody RetrieveRequestDTO request);

    // 如果需要其他 API，可以继续添加

    /**
     * 检索服务客户端专用配置类
     *
     * 配置认证拦截器和超时设置
     */
    @Configuration
    class RetrievalServiceClientConfiguration {

        @Bean
        public UserFeignClientInterceptor userFeignClientInterceptor() {
            return new UserFeignClientInterceptor();
        }

        @Bean
        public Request.Options feignRequestOptions() {
            // 设置连接超时和读取超时 - 优化为更快的响应时间
            return new Request.Options(
                3000, // 连接超时 3 秒 - 减少连接等待时间
                8000 // 读取超时 8 秒 - 大幅减少读取超时，避免用户感觉卡死
            );
        }
    }
}
