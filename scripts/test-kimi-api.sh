#!/bin/bash

# =====================================================
# Kimi API 测试脚本
# =====================================================
# 
# 此脚本用于测试 Kimi API 配置是否正确
# 
# 使用方式：
# chmod +x scripts/test-kimi-api.sh
# ./scripts/test-kimi-api.sh
# 
# =====================================================

echo "🚀 开始测试 Kimi API 配置..."
echo "=================================="

# 检查应用是否运行
echo "📋 1. 检查应用状态..."
if docker ps | grep -q "whiskerguardaiservice-app"; then
    echo "✅ 应用服务正在运行"
else
    echo "❌ 应用服务未运行，请先启动服务"
    echo "   执行: docker-compose up -d"
    exit 1
fi

# 检查数据库配置
echo ""
echo "📋 2. 检查数据库中的 Kimi 配置..."
DB_CHECK=$(docker exec whiskerguardaiservice-mysql-1 mysql -u root -e "
USE whiskerguardaiservice; 
SELECT 
    CASE 
        WHEN api_key = 'sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ' THEN 'TEST_KEY'
        WHEN api_key = 'YOUR_ACTUAL_VOLCANO_ENGINE_API_KEY' THEN 'PLACEHOLDER'
        WHEN api_key IS NULL OR api_key = '' THEN 'EMPTY'
        ELSE 'CONFIGURED'
    END as key_status,
    status
FROM ai_tool 
WHERE tool_key = 'kimi' AND is_deleted = false;" 2>/dev/null)

if echo "$DB_CHECK" | grep -q "CONFIGURED.*AVAILABLE"; then
    echo "✅ 数据库配置看起来正常"
elif echo "$DB_CHECK" | grep -q "TEST_KEY\|PLACEHOLDER"; then
    echo "❌ 检测到测试用 API Key，请更新为真实的火山引擎 API Key"
    echo "   参考: docs/kimi-api-configuration-guide.md"
    echo "   或执行: docker exec -it whiskerguardaiservice-mysql-1 mysql -u root < kimi-api-key-update.sql"
else
    echo "⚠️  数据库配置状态未知，请手动检查"
fi

# 测试 API 调用
echo ""
echo "📋 3. 测试 API 调用..."

# 获取应用端口
APP_PORT=$(docker port whiskerguardaiservice-app-1 8085/tcp 2>/dev/null | cut -d: -f2)
if [ -z "$APP_PORT" ]; then
    APP_PORT="8085"
fi

API_URL="http://localhost:${APP_PORT}/api/ai/invoke"

echo "   API 地址: $API_URL"

# 构造测试请求
TEST_REQUEST='{
    "toolType": "kimi",
    "prompt": "你好，请简单介绍一下自己。",
    "tenantId": 1,
    "employeeId": 1,
    "metadata": {}
}'

echo "   发送测试请求..."

# 发送请求并检查响应
RESPONSE=$(curl -s -w "\n%{http_code}" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d "$TEST_REQUEST" \
    "$API_URL" 2>/dev/null)

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "   HTTP 状态码: $HTTP_CODE"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ API 调用成功！"
    
    # 检查响应内容
    if echo "$RESPONSE_BODY" | grep -q "调用Kimi API失败"; then
        echo "❌ API 调用返回错误："
        echo "$RESPONSE_BODY" | grep -o '"response":"[^"]*"' | sed 's/"response":"//;s/"$//'
    else
        echo "✅ API 响应正常"
        echo "   响应预览: $(echo "$RESPONSE_BODY" | head -c 100)..."
    fi
elif [ "$HTTP_CODE" = "401" ]; then
    echo "❌ 认证失败 (401)，请检查 API Key 配置"
elif [ "$HTTP_CODE" = "500" ]; then
    echo "❌ 服务器内部错误 (500)，请检查应用日志"
    echo "   查看日志: docker logs whiskerguardaiservice-app-1 --tail 50"
else
    echo "❌ API 调用失败，HTTP 状态码: $HTTP_CODE"
    echo "   响应内容: $RESPONSE_BODY"
fi

# 检查应用日志中的错误
echo ""
echo "📋 4. 检查最近的应用日志..."
RECENT_LOGS=$(docker logs whiskerguardaiservice-app-1 --tail 20 2>/dev/null | grep -i "kimi\|error\|exception" | tail -5)

if [ -n "$RECENT_LOGS" ]; then
    echo "   最近的相关日志:"
    echo "$RECENT_LOGS"
else
    echo "   未发现明显的错误日志"
fi

echo ""
echo "=================================="
echo "🏁 测试完成"
echo ""
echo "📚 如果遇到问题，请参考："
echo "   - docs/kimi-api-configuration-guide.md"
echo "   - 火山引擎控制台: https://console.volcengine.com/ark"
echo "   - 应用日志: docker logs whiskerguardaiservice-app-1 -f"
echo ""
echo "🔧 常用命令："
echo "   重启应用: docker-compose restart whiskerguardaiservice"
echo "   查看配置: docker exec whiskerguardaiservice-mysql-1 mysql -u root -e 'USE whiskerguardaiservice; SELECT * FROM ai_tool WHERE tool_key=\"kimi\";'"
echo "   更新配置: docker exec -it whiskerguardaiservice-mysql-1 mysql -u root < kimi-api-key-update.sql"
