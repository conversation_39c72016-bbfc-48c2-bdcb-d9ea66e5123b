-- =====================================================
-- 修复 Kimi API 配置 - 更新为 Moonshot AI 端点
-- =====================================================
-- 
-- 问题：当前配置使用的是火山引擎端点，但 API Key 是 Moonshot AI 的
-- 解决：更新为正确的 Moonshot AI API 端点和配置
-- 
-- 执行方式：
-- docker exec -it whiskerguardaiservice-mysql-1 mysql -u root < fix-kimi-moonshot-config.sql
-- 
-- =====================================================

USE whiskerguardaiservice;

-- 1. 查看当前配置
SELECT 
    '=== 修复前的配置 ===' as info,
    tool_key, 
    api_url, 
    path,
    model_provider,
    metadata,
    remark
FROM ai_tool 
WHERE tool_key = 'kimi' AND is_deleted = false;

-- 2. 更新为正确的 Moonshot AI 配置
UPDATE ai_tool SET 
    api_url = 'https://api.moonshot.cn/v1',           -- Moonshot AI API 端点
    path = '/chat/completions',                       -- 标准 OpenAI 兼容路径
    model_provider = 'Moonshot AI',                   -- 更新提供商名称
    metadata = '{"model":"moonshot-v1-8k","temperature":0.7,"max_tokens":2048,"supports_streaming":true}',  -- Moonshot 模型配置
    remark = 'Moonshot AI Kimi 模型 - 已修复 API 端点配置',
    updated_at = NOW(),
    updated_by = 'system'
WHERE tool_key = 'kimi' 
  AND is_deleted = false;

-- 3. 验证更新结果
SELECT 
    '=== 修复后的配置 ===' as info,
    tool_key, 
    api_url, 
    path,
    model_provider,
    metadata,
    remark,
    updated_at
FROM ai_tool 
WHERE tool_key = 'kimi' AND is_deleted = false;

-- 4. 配置验证检查
SELECT 
    CASE 
        WHEN api_url = 'https://api.moonshot.cn/v1' THEN '✅ API URL 已更新为 Moonshot'
        ELSE '❌ API URL 更新失败'
    END as api_url_status,
    
    CASE 
        WHEN path = '/chat/completions' THEN '✅ API Path 配置正确'
        ELSE '❌ API Path 配置错误'
    END as api_path_status,
    
    CASE 
        WHEN model_provider = 'Moonshot AI' THEN '✅ 模型提供商已更新'
        ELSE '❌ 模型提供商未更新'
    END as provider_status,
    
    CASE 
        WHEN status = 'AVAILABLE' THEN '✅ 工具状态正常'
        ELSE '❌ 工具状态异常'
    END as tool_status
FROM ai_tool 
WHERE tool_key = 'kimi' AND is_deleted = false;

-- 5. 显示配置完成提示
SELECT '
=====================================================
🎉 Kimi API 配置修复完成！

📋 修复内容：
- API URL: https://api.moonshot.cn/v1
- 模型提供商: Moonshot AI  
- 默认模型: moonshot-v1-8k
- API Key: 保持不变（已验证有效）

🚀 下一步操作：
1. 重启应用服务
2. 测试 API 调用
3. 验证功能正常

🔧 测试命令：
curl -X POST http://localhost:8085/api/ai/invoke \\
  -H "Content-Type: application/json" \\
  -d "{
    \"toolType\": \"kimi\",
    \"prompt\": \"你好，请简单介绍一下自己。\",
    \"tenantId\": 1,
    \"employeeId\": 1,
    \"metadata\": {}
  }"

📚 相关文档：
- Moonshot AI 文档: https://platform.moonshot.cn/docs
- 项目配置指南: docs/kimi-api-configuration-guide.md
=====================================================
' as completion_guide;

-- 6. 可选：如果需要回滚到火山引擎配置
-- UPDATE ai_tool SET 
--     api_url = 'https://ark.cn-beijing.volces.com/api/v3',
--     path = '/chat/completions',
--     model_provider = 'ByteDance',
--     metadata = '{"model":"doubao-seed-1-6-250615","temperature":0.7,"max_tokens":2048}',
--     remark = '已回滚到火山引擎配置',
--     updated_at = NOW()
-- WHERE tool_key = 'kimi' AND is_deleted = false;
