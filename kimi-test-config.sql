-- 临时解决方案：使用官方示例中的 API Key 格式进行测试
-- 注意：这个 API Key 是示例中的，您需要替换为您实际的 Key

-- 使用官方示例中的 API Key 格式更新配置
UPDATE ai_tool SET 
    api_key = '9ce5c8e2-0ad9-4c60-8b3f-39f5dce040ee',  -- 使用官方示例中的格式
    updated_at = NOW(),
    remark = '测试用API Key，需要替换为实际的认证信息'
WHERE tool_key = 'kimi' 
  AND is_deleted = false;

-- 如果需要重新插入（如果记录不存在）
INSERT INTO ai_tool (
    tenant_id, name, tool_key, version, api_url, api_key, auth_type, path, 
    status, weight, max_concurrent_calls, is_model, model_category, model_provider,
    remark, metadata, created_by, created_at, updated_by, updated_at, is_deleted
) 
SELECT 1, 'Kimi AI模型', 'kimi', 1, 
       'https://ark.cn-beijing.volces.com/api/v3',
       '9ce5c8e2-0ad9-4c60-8b3f-39f5dce040ee',  -- 官方示例格式
       'Bearer', '/chat/completions', 'AVAILABLE', 100, 5, true, 
       'TEXT_GENERATION', 'ByteDance',
       '使用官方示例API Key进行测试，需要替换为实际Key',
       '{"model":"doubao-seed-1-6-250615","temperature":0.7,"max_tokens":2048}',
       'system', NOW(), 'system', NOW(), false
WHERE NOT EXISTS (
    SELECT 1 FROM ai_tool WHERE tool_key = 'kimi' AND is_deleted = false
);

-- 验证结果
SELECT tool_key, api_key, api_url, path, status FROM ai_tool WHERE tool_key = 'kimi';
