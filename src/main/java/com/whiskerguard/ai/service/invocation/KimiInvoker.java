/**
 * Kimi 调用实现类
 * <p>
 * 负责与字节跳动火山引擎的 Kimi API (豆包) 进行通信，支持 RAG 增强功能。
 * API 端点：https://ark.cn-beijing.volces.com/api/v3/chat/completions
 * 模型：doubao-seed-1-6-250615
 */
package com.whiskerguard.ai.service.invocation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import io.github.resilience4j.bulkhead.annotation.Bulkhead;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

@Component
@SuppressWarnings("unchecked")
public class KimiInvoker implements AiToolInvoker {

    private final Logger log = LoggerFactory.getLogger(KimiInvoker.class);
    private final WebClient.Builder webClientBuilder;
    private final RagHelper ragHelper;
    private final ObjectMapper objectMapper;

    public KimiInvoker(WebClient.Builder webClientBuilder, RagHelper ragHelper, ObjectMapper objectMapper) {
        this.webClientBuilder = webClientBuilder;
        this.ragHelper = ragHelper;
        this.objectMapper = objectMapper;
    }

    /**
     * 验证 API Key 格式是否有效
     * @param apiKey API Key
     * @return 验证结果和错误信息
     */
    private ValidationResult validateApiKey(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return new ValidationResult(false, "API Key 不能为空");
        }

        // 检查是否是明显的测试/占位符 Key
        if (apiKey.contains("test") || apiKey.contains("placeholder") || apiKey.contains("example")) {
            return new ValidationResult(false, "检测到测试用 API Key，请配置真实的 Moonshot AI API Key");
        }

        // 注意：sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ 是有效的 Moonshot API Key
        // 不再将其视为测试密钥

        // 基本长度检查
        if (apiKey.length() < 10) {
            return new ValidationResult(false, "API Key 长度过短，可能不是有效的密钥");
        }

        return new ValidationResult(true, "API Key 格式验证通过");
    }

    /**
     * 验证结果内部类
     */
    private static class ValidationResult {
        private final boolean valid;
        private final String message;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String getToolKey() {
        return "kimi";
    }

    @Override
    @Bulkhead(name = "kimi", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "kimi")
    @Retry(name = "kimi")
    public AiResult invoke(AiInvocationRequestDTO dto, AiTool cfg) {
        try {
            // 验证 API Key
            ValidationResult keyValidation = validateApiKey(cfg.getApiKey());
            if (!keyValidation.isValid()) {
                throw new IllegalArgumentException("Kimi API Key 验证失败: " + keyValidation.getMessage() +
                    "，请参考 docs/kimi-api-configuration-guide.md 进行配置");
            }

            if (cfg.getApiUrl() == null || cfg.getApiUrl().trim().isEmpty()) {
                throw new IllegalArgumentException("Kimi API URL 未配置");
            }

            // 根据 API Key 格式决定认证方式
            String authHeader;
            if (cfg.getApiKey().startsWith("sk-")) {
                // OpenAI 格式的 key，直接使用 Bearer
                authHeader = "Bearer " + cfg.getApiKey();
            } else {
                // 火山引擎格式的 key，也使用 Bearer
                authHeader = "Bearer " + cfg.getApiKey();
            }

            log.debug("Kimi API 调用配置 - URL: {}, Path: {}, API Key前缀: {}, Auth: Bearer ***",
                cfg.getApiUrl(), cfg.getPath(),
                cfg.getApiKey().substring(0, Math.min(10, cfg.getApiKey().length())) + "***");
            
            WebClient client = webClientBuilder
                .baseUrl(cfg.getApiUrl())
                .defaultHeader("Authorization", authHeader)
                .defaultHeader("Content-Type", "application/json")
                .build();

            // 使用 RAG 增强提示词
            String originalPrompt = dto.getPrompt();
            String enhancedPrompt = ragHelper.enhancePromptWithRag(dto);
            log.debug("原始提示词: {}", originalPrompt);
            log.debug("增强提示词: {}", enhancedPrompt);

            // 构建Kimi API请求体 - 使用 Moonshot AI 格式
            Map<String, Object> body = new HashMap<>();

            // 从配置中获取模型名称，默认使用 moonshot-v1-8k
            String modelName = "moonshot-v1-8k"; // 默认模型
            if (cfg.getMetadata() != null && cfg.getMetadata().contains("moonshot")) {
                // 如果元数据中包含模型信息，尝试解析
                try {
                    if (cfg.getMetadata().contains("moonshot-v1-8k")) {
                        modelName = "moonshot-v1-8k";
                    } else if (cfg.getMetadata().contains("moonshot-v1-32k")) {
                        modelName = "moonshot-v1-32k";
                    } else if (cfg.getMetadata().contains("moonshot-v1-128k")) {
                        modelName = "moonshot-v1-128k";
                    }
                } catch (Exception e) {
                    log.debug("解析模型名称失败，使用默认模型: {}", modelName);
                }
            }

            body.put("model", modelName);
            body.put(
                "messages",
                new Object[] {
                    new HashMap<String, String>() {
                        {
                            put("role", "user");
                            put("content", enhancedPrompt);
                        }
                    },
                }
            );

            // 添加其他可选参数
            if (dto.getMetadata() != null) {
                // 如果元数据中有temperature, max_tokens等参数，添加到请求中
                if (dto.getMetadata().containsKey("temperature")) {
                    body.put("temperature", dto.getMetadata().get("temperature"));
                }
                if (dto.getMetadata().containsKey("max_tokens")) {
                    body.put("max_tokens", dto.getMetadata().get("max_tokens"));
                }
            }

            log.debug("Kimi请求体: {}", body);

            long start = System.currentTimeMillis();
            Map<String, Object> resp = client.post().uri(cfg.getPath()).bodyValue(body).retrieve().bodyToMono(Map.class).block();
            long duration = System.currentTimeMillis() - start;

            log.debug("Kimi响应: {}", resp);

            // Kimi 响应解析
            Map<String, Object> choices = (Map<String, Object>) ((List<Object>) resp.get("choices")).get(0);
            Map<String, Object> message = (Map<String, Object>) choices.get("message");
            String content = (String) message.get("content");

            // 提取使用量信息
            Map<String, Object> usage = (Map<String, Object>) resp.get("usage");
            if (usage == null) {
                usage = new HashMap<>();
                usage.put("total_tokens", 0);
            }

            return AiResult.builder().content(content).usage(usage).durationMs(duration).build();
        } catch (Exception e) {
            log.error("调用Kimi API失败 - URL: {}, API Key前缀: {}, 错误详情: {}",
                cfg.getApiUrl(),
                cfg.getApiKey() != null ? cfg.getApiKey().substring(0, Math.min(10, cfg.getApiKey().length())) + "***" : "null",
                e.getMessage(), e);

            // 根据错误类型提供更详细的错误信息
            String errorMessage = "调用Kimi API失败: " + e.getMessage();
            if (e.getMessage() != null) {
                if (e.getMessage().contains("401") || e.getMessage().contains("Unauthorized")) {
                    errorMessage += "\n可能的原因：\n1. API Key 无效或已过期\n2. 未开通对应的模型服务\n3. API Key 格式不正确\n请参考文档 docs/kimi-api-configuration-guide.md 进行配置";
                } else if (e.getMessage().contains("403") || e.getMessage().contains("Forbidden")) {
                    errorMessage += "\n可能的原因：\n1. API Key 权限不足\n2. 账号余额不足\n3. 请求频率超限";
                } else if (e.getMessage().contains("timeout") || e.getMessage().contains("connect")) {
                    errorMessage += "\n可能的原因：\n1. 网络连接问题\n2. 火山引擎服务暂时不可用\n3. 请求超时";
                }
            }

            // 返回一个包含错误信息的结果
            Map<String, Object> errorUsage = new HashMap<>();
            errorUsage.put("error", e.getMessage());
            errorUsage.put("error_type", e.getClass().getSimpleName());
            errorUsage.put("api_url", cfg.getApiUrl());
            errorUsage.put("model", "moonshot-v1-8k");
            return AiResult.builder().content(errorMessage).usage(errorUsage).durationMs(0).build();
        }
    }

    @Override
    @Bulkhead(name = "kimi-stream", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "kimi-stream")
    public Flux<AiStreamResponseDTO> invokeStream(AiStreamRequestDTO dto, AiTool cfg) {
        WebClient client = webClientBuilder
            .baseUrl(cfg.getApiUrl())
            .defaultHeader("Authorization", "Bearer " + cfg.getApiKey())
            .defaultHeader("Content-Type", "application/json")
            .build();

        // 使用 RAG 增强提示词
        String originalPrompt = dto.getPrompt();
        String enhancedPrompt = ragHelper.enhancePromptWithRag(
            new AiInvocationRequestDTO(dto.getToolKey(), dto.getPrompt(), dto.getMetadata(), dto.getTenantId(), dto.getEmployeeId())
        );
        log.debug("流式调用 - 原始提示词: {}", originalPrompt);
        log.debug("流式调用 - 增强提示词: {}", enhancedPrompt);

        // 构建Kimi API请求体 - 使用 Moonshot AI 格式
        Map<String, Object> body = new HashMap<>();

        // 从配置中获取模型名称，默认使用 moonshot-v1-8k
        String modelName = "moonshot-v1-8k"; // 默认模型
        if (cfg.getMetadata() != null && cfg.getMetadata().contains("moonshot")) {
            // 如果元数据中包含模型信息，尝试解析
            try {
                if (cfg.getMetadata().contains("moonshot-v1-8k")) {
                    modelName = "moonshot-v1-8k";
                } else if (cfg.getMetadata().contains("moonshot-v1-32k")) {
                    modelName = "moonshot-v1-32k";
                } else if (cfg.getMetadata().contains("moonshot-v1-128k")) {
                    modelName = "moonshot-v1-128k";
                }
            } catch (Exception e) {
                log.debug("解析模型名称失败，使用默认模型: {}", modelName);
            }
        }

        body.put("model", modelName);
        body.put(
            "messages",
            new Object[] {
                new HashMap<String, String>() {
                    {
                        put("role", "user");
                        put("content", enhancedPrompt);
                    }
                },
            }
        );
        body.put("stream", true); // 启用流式输出

        // 添加其他可选参数
        if (dto.getMetadata() != null) {
            // 如果元数据中有temperature, max_tokens等参数，添加到请求中
            if (dto.getMetadata().containsKey("temperature")) {
                body.put("temperature", dto.getMetadata().get("temperature"));
            }
            if (dto.getMetadata().containsKey("max_tokens")) {
                body.put("max_tokens", dto.getMetadata().get("max_tokens"));
            }
        }

        log.debug("Kimi流式请求体: {}", body);

        // 用于累积完整响应以计算最终使用量
        AtomicReference<StringBuilder> fullResponseBuilder = new AtomicReference<>(new StringBuilder());

        // 发起流式请求
        return client
            .post()
            .uri(cfg.getPath())
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body)
            .accept(MediaType.TEXT_EVENT_STREAM)
            .retrieve()
            .bodyToFlux(String.class)
            .map(chunk -> {
                try {
                    // 处理 SSE 格式的响应
                    if (chunk.startsWith("data: ")) {
                        chunk = chunk.substring(6);
                    }

                    // 处理流结束标记
                    if ("[DONE]".equals(chunk.trim())) {
                        // 创建最终使用量统计
                        Map<String, Object> usage = new HashMap<>();
                        usage.put("total_tokens", fullResponseBuilder.get().length() / 4); // 粗略估计
                        return new AiStreamResponseDTO("", usage);
                    }

                    // 解析 JSON 响应
                    Map<String, Object> response = objectMapper.readValue(chunk, Map.class);

                    // 提取内容
                    Map<String, Object> choices = (Map<String, Object>) ((List<Object>) response.get("choices")).get(0);
                    Map<String, Object> delta = (Map<String, Object>) choices.get("delta");

                    if (delta != null && delta.containsKey("content")) {
                        String content = (String) delta.get("content");
                        fullResponseBuilder.get().append(content);
                        return new AiStreamResponseDTO(content, false);
                    }

                    // 如果没有 delta 或 content，返回空内容
                    return new AiStreamResponseDTO("", false);
                } catch (JsonProcessingException e) {
                    log.error("解析Kimi流式响应失败", e);
                    return new AiStreamResponseDTO("解析Kimi流式响应失败: " + e.getMessage());
                }
            })
            .onErrorResume(e -> {
                log.error("Kimi流式调用失败", e);
                return Flux.just(new AiStreamResponseDTO("Kimi流式调用失败: " + e.getMessage()));
            });
    }
}
