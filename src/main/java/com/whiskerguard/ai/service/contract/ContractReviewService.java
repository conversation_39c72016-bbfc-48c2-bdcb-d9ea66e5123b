/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewService.java
 * 包    名：com.whiskerguard.ai.service.contract
 * 描    述：合同智能审查核心服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.contract;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.client.GeneralServiceClient;
import com.whiskerguard.ai.client.RegulatoryServiceClient;
import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.client.dto.*;
import com.whiskerguard.ai.domain.ContractReview;
import com.whiskerguard.ai.domain.enumeration.ReviewStatus;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.repository.ContractReviewRepository;
import com.whiskerguard.ai.service.contract.context.*;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.dto.ContractReviewRequestDTO;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.util.AiResponseParseUtil;
import com.whiskerguard.ai.util.UniversalAiResponseParser;
import com.whiskerguard.ai.web.rest.ContractReviewController;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合同智能审查核心服务
 * <p>
 * 提供完整的合同智能审查功能，包括：
 * 1. 多租户数据隔离
 * 2. 多维度信息检索和整合
 * 3. AI智能分析
 * 4. 结构化结果输出
 */
@Service
@Transactional
public class ContractReviewService {

    private final Logger log = LoggerFactory.getLogger(ContractReviewService.class);

    private final RetrievalServiceClient retrievalServiceClient;
    private final GeneralServiceClient generalServiceClient;
    private final RegulatoryServiceClient regulatoryServiceClient;
    private final AiInvocationService aiInvocationService;
    private final ContractPartyExtractor partyExtractor;
    private final ContractReviewPromptBuilder promptBuilder;
    private final ContractReviewRepository reviewRepository;
    private final ObjectMapper objectMapper;
    private final UniversalAiResponseParser universalParser;

    public ContractReviewService(
        RetrievalServiceClient retrievalServiceClient,
        GeneralServiceClient generalServiceClient,
        RegulatoryServiceClient regulatoryServiceClient,
        AiInvocationService aiInvocationService,
        ContractPartyExtractor partyExtractor,
        ContractReviewPromptBuilder promptBuilder,
        ContractReviewRepository reviewRepository,
        ObjectMapper objectMapper,
        UniversalAiResponseParser universalParser
    ) {
        this.retrievalServiceClient = retrievalServiceClient;
        this.generalServiceClient = generalServiceClient;
        this.regulatoryServiceClient = regulatoryServiceClient;
        this.aiInvocationService = aiInvocationService;
        this.partyExtractor = partyExtractor;
        this.promptBuilder = promptBuilder;
        this.reviewRepository = reviewRepository;
        this.objectMapper = objectMapper;
        this.universalParser = universalParser;
    }

    /**
     * 合同智能审查主流程（多租户隔离）
     */
    public ContractReviewResponseDTO reviewContract(ContractReviewRequestDTO request) {
        long startTime = System.currentTimeMillis();

        // 多租户数据隔离验证
        validateTenantAccess(request.getTenantId(), request.getEmployeeId());

        log.info(
            "开始合同智能审查 - 租户ID: {}, 员工ID: {}, 合同类型: {}",
            request.getTenantId(),
            request.getEmployeeId(),
            request.getContractType()
        );

        // 创建审查记录（多租户隔离）
        ContractReview reviewRecord = createReviewRecord(request);
        reviewRecord = reviewRepository.save(reviewRecord);

        try {
            // 1. 提取合同关联方
            List<ContractPartyExtractor.ContractPartyInfo> parties = partyExtractor.extractParties(request.getContractContent());
            log.debug("租户 {} 提取到关联方数量: {}", request.getTenantId(), parties.size());

            // 2. 并行获取多维度信息（所有调用都带租户ID）
            CompletableFuture<EnterpriseInfoContext> enterpriseInfoFuture = CompletableFuture.supplyAsync(() ->
                getEnterpriseInfo(parties, request.getTenantId())
            );

            CompletableFuture<LegalRegulatoryContext> legalContextFuture = CompletableFuture.supplyAsync(() ->
                getLegalRegulatoryContext(request, parties)
            );

            CompletableFuture<InternalPolicyContext> internalPolicyFuture = CompletableFuture.supplyAsync(() ->
                getInternalPolicyContext(request.getTenantId(), request.getContractType())
            );

            CompletableFuture<HistoricalCaseContext> historicalCaseFuture = CompletableFuture.supplyAsync(() ->
                getHistoricalCaseContext(request)
            );

            // 3. 等待所有并行任务完成
            EnterpriseInfoContext enterpriseInfo = enterpriseInfoFuture.get(120, TimeUnit.SECONDS);
            LegalRegulatoryContext legalContext = legalContextFuture.get(120, TimeUnit.SECONDS);
            InternalPolicyContext internalPolicy = internalPolicyFuture.get(120, TimeUnit.SECONDS);
            HistoricalCaseContext historicalCase = historicalCaseFuture.get(120, TimeUnit.SECONDS);

            // 4. 构建多维度增强提示词
            String enhancedPrompt = promptBuilder.buildComprehensiveReviewPrompt(
                request.getContractContent(),
                request.getContractType(),
                parties,
                enterpriseInfo,
                legalContext,
                internalPolicy,
                historicalCase,
                request
            );

            // 5. 调用LLM进行智能分析（带租户隔离）
            String aiModel = "kimi"; // 默认模型
            if (request.getMetadata() != null && request.getMetadata().containsKey("aiModel")) {
                aiModel = (String) request.getMetadata().get("aiModel");
            }
            AiInvocationRequestDTO aiRequest = new AiInvocationRequestDTO(
                aiModel, // 使用指定的AI模型
                enhancedPrompt,
                buildMetadata(request, reviewRecord.getId()),
                request.getTenantId(),
                request.getEmployeeId()
            );

            AiRequestDTO aiResponse = aiInvocationService.invoke(aiRequest);

            // 6. 解析并结构化结果
            ContractReviewResponseDTO response = parseAndStructureResult(
                aiResponse.getResponse(),
                parties,
                enterpriseInfo,
                legalContext,
                internalPolicy,
                request.getTenantId()
            );

            // 7. 更新审查记录
            long duration = System.currentTimeMillis() - startTime;
            updateReviewRecord(reviewRecord, response, aiResponse, duration);
            response.setReviewId(reviewRecord.getId());
            response.setReviewTime(Instant.now());
            response.setReviewDuration(duration);

            log.info(
                "租户 {} 合同智能审查完成，风险等级: {}, 审查ID: {}, 耗时: {}ms",
                request.getTenantId(),
                response.getOverallRiskLevel(),
                reviewRecord.getId(),
                duration
            );

            return response;
        } catch (Exception e) {
            log.error("租户 {} 合同智能审查失败", request.getTenantId(), e);
            updateReviewRecordWithError(reviewRecord, e);
            throw new RuntimeException("合同审查处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取企业信息（天眼查 + RAG）
     */
    private EnterpriseInfoContext getEnterpriseInfo(List<ContractPartyExtractor.ContractPartyInfo> parties, Long tenantId) {
        EnterpriseInfoContext context = new EnterpriseInfoContext();

        for (ContractPartyExtractor.ContractPartyInfo party : parties) {
            if (party.getType() == com.whiskerguard.ai.domain.enumeration.PartyType.COMPANY) {
                try {
                    // 1. 从天眼查获取实时企业信息
                    CompanyBasicInfoDTO basicInfo = generalServiceClient.getCompanyBasicInfo(party.getName(), tenantId);
                    CompanyRiskInfoDTO riskInfo = generalServiceClient.getCompanyRiskInfo(party.getName(), tenantId);
                    CompanyLawsuitInfoDTO lawsuitInfo = generalServiceClient.getCompanyLawsuitInfo(party.getName(), tenantId);
                    CompanyCreditInfoDTO creditInfo = generalServiceClient.getCompanyCreditInfo(party.getName(), tenantId);

                    // 2. 从RAG检索历史风险案例和行业信息
                    String ragQuery = String.format(
                        "企业：%s 行业：%s 风险案例 合规问题 监管处罚",
                        party.getName(),
                        basicInfo != null ? basicInfo.getIndustry() : ""
                    );
                    RetrieveRequestDTO ragRequest = new RetrieveRequestDTO(ragQuery, 3, "cosine");
                    RetrieveResponseDTO ragResponse = retrievalServiceClient.retrieve(tenantId.toString(), ragRequest);

                    // 3. 整合信息
                    EnterpriseInfoContext.EnterpriseInfo enterpriseInfo = new EnterpriseInfoContext.EnterpriseInfo(
                        party.getName(),
                        basicInfo,
                        riskInfo,
                        lawsuitInfo,
                        creditInfo,
                        ragResponse.getResults()
                    );

                    context.addEnterpriseInfo(enterpriseInfo);
                } catch (Exception e) {
                    log.warn("获取企业信息失败: {}, 错误: {}", party.getName(), e.getMessage());
                    // 降级处理：仅使用RAG检索
                    context.addEnterpriseInfo(getEnterpriseInfoFromRAGOnly(party.getName(), tenantId));
                }
            }
        }

        return context;
    }

    /**
     * RAG降级处理：仅从RAG获取企业信息
     */
    private EnterpriseInfoContext.EnterpriseInfo getEnterpriseInfoFromRAGOnly(String companyName, Long tenantId) {
        try {
            String ragQuery = String.format("企业：%s 基本信息 风险评估", companyName);
            RetrieveRequestDTO ragRequest = new RetrieveRequestDTO(ragQuery, 5, "cosine");
            RetrieveResponseDTO ragResponse = retrievalServiceClient.retrieve(tenantId.toString(), ragRequest);

            return new EnterpriseInfoContext.EnterpriseInfo(companyName, null, null, null, null, ragResponse.getResults());
        } catch (Exception e) {
            log.warn("RAG降级处理也失败: {}", e.getMessage());
            return new EnterpriseInfoContext.EnterpriseInfo(companyName, null, null, null, null, new ArrayList<>());
        }
    }

    /**
     * 多租户访问权限验证
     */
    private void validateTenantAccess(Long tenantId, Long employeeId) {
        if (tenantId == null || employeeId == null) {
            throw new IllegalArgumentException("租户ID和员工ID不能为空");
        }

        // TODO: 实现具体的租户权限验证逻辑
        // 可以调用用户服务或从JWT token中验证
        log.debug("验证租户 {} 员工 {} 访问权限", tenantId, employeeId);
    }

    /**
     * 创建审查记录（多租户隔离）
     */
    private ContractReview createReviewRecord(ContractReviewRequestDTO request) {
        ContractReview record = new ContractReview();
        record.setTenantId(request.getTenantId()); // 租户隔离
        record.setEmployeeId(request.getEmployeeId());
        record.setContractType(request.getContractType());
        record.setContractTitle(request.getContractTitle());
        record.setContractContent(request.getContractContent());
        record.setStatus(ReviewStatus.PROCESSING);
        record.setReviewStartTime(Instant.now());
        record.setCreatedAt(Instant.now());
        record.setCreatedBy(request.getEmployeeId().toString());
        record.setVersion(1);
        record.setIsDeleted(false);

        return record;
    }

    /**
     * 获取法律法规上下文（多租户 + 法规服务）
     */
    private LegalRegulatoryContext getLegalRegulatoryContext(
        ContractReviewRequestDTO request,
        List<ContractPartyExtractor.ContractPartyInfo> parties
    ) {
        try {
            LegalRegulatoryContext context = new LegalRegulatoryContext();

            // 1. 从法规制度服务获取相关法律法规
            List<LegalRegulationDTO> contractTypeRegulations = regulatoryServiceClient.getRegulationsByContractType(
                request.getContractType(),
                request.getTenantId()
            );
            context.setContractTypeRegulations(contractTypeRegulations);

            // 2. 根据企业所属行业获取行业法规
            Set<String> industries = extractIndustriesFromParties(parties);
            for (String industry : industries) {
                List<LegalRegulationDTO> industryRegulations = regulatoryServiceClient.getRegulationsByIndustry(
                    industry,
                    request.getTenantId()
                );
                context.addIndustryRegulations(industry, industryRegulations);
            }

            // 3. 获取合规检查清单
            ComplianceChecklistDTO checklist = regulatoryServiceClient.getComplianceChecklist(
                request.getTenantId(),
                request.getContractType(),
                industries.isEmpty() ? null : industries.iterator().next()
            );
            context.setComplianceChecklist(checklist);

            // 4. 从RAG检索补充法规解释和案例
            String ragQuery = String.format("合同类型：%s 法律法规解释 司法解释 典型案例", request.getContractType());
            RetrieveRequestDTO ragRequest = new RetrieveRequestDTO(ragQuery, 5, "cosine");
            RetrieveResponseDTO ragResponse = retrievalServiceClient.retrieve(request.getTenantId().toString(), ragRequest);
            context.setRagSupplementaryInfo(ragResponse.getResults());

            log.debug("租户 {} 获取法律法规上下文完成，法规数量: {}", request.getTenantId(), contractTypeRegulations.size());

            return context;
        } catch (Exception e) {
            log.warn("租户 {} 获取法律法规上下文失败: {}", request.getTenantId(), e.getMessage());
            return new LegalRegulatoryContext(); // 返回空上下文，不影响主流程
        }
    }

    /**
     * 获取企业内部制度上下文（租户隔离）
     */
    private InternalPolicyContext getInternalPolicyContext(Long tenantId, String contractType) {
        try {
            // 获取租户的内部制度
            List<InternalPolicyDTO> allPolicies = regulatoryServiceClient.getInternalPolicies(tenantId, null);

            // 获取合同相关的内部制度
            List<InternalPolicyDTO> contractPolicies = regulatoryServiceClient.getInternalPolicies(tenantId, "CONTRACT");

            InternalPolicyContext context = new InternalPolicyContext(allPolicies, contractPolicies);

            log.debug("租户 {} 获取内部制度上下文完成，制度数量: {}", tenantId, allPolicies.size());
            return context;
        } catch (Exception e) {
            log.warn("租户 {} 获取内部制度上下文失败: {}", tenantId, e.getMessage());
            return new InternalPolicyContext();
        }
    }

    /**
     * 获取历史案例上下文（租户隔离的RAG检索）
     */
    private HistoricalCaseContext getHistoricalCaseContext(ContractReviewRequestDTO request) {
        try {
            // 1. 检索租户历史审查案例
            String tenantHistoryQuery = String.format("租户：%s 合同审查 历史案例 风险点", request.getTenantId());
            RetrieveRequestDTO tenantRequest = new RetrieveRequestDTO(tenantHistoryQuery, 3, "cosine");
            RetrieveResponseDTO tenantResponse = retrievalServiceClient.retrieve(request.getTenantId().toString(), tenantRequest);

            // 2. 检索行业通用案例
            String industryQuery = String.format("合同类型：%s 行业案例 风险分析 最佳实践", request.getContractType());
            RetrieveRequestDTO industryRequest = new RetrieveRequestDTO(industryQuery, 5, "cosine");
            RetrieveResponseDTO industryResponse = retrievalServiceClient.retrieve(request.getTenantId().toString(), industryRequest);

            return new HistoricalCaseContext(tenantResponse.getResults(), industryResponse.getResults());
        } catch (Exception e) {
            log.warn("租户 {} 获取历史案例上下文失败: {}", request.getTenantId(), e.getMessage());
            return new HistoricalCaseContext();
        }
    }

    /**
     * 从关联方中提取行业信息
     */
    private Set<String> extractIndustriesFromParties(List<ContractPartyExtractor.ContractPartyInfo> parties) {
        Set<String> industries = new HashSet<>();

        // 这里可以根据企业名称推断行业，或者从已获取的企业信息中提取
        // 简化实现：返回一些通用行业
        industries.add("制造业");
        industries.add("服务业");

        return industries;
    }

    /**
     * 构建AI调用元数据
     */
    private Map<String, Object> buildMetadata(ContractReviewRequestDTO request, Long reviewId) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("reviewId", reviewId);
        metadata.put("contractType", request.getContractType());
        metadata.put("tenantId", request.getTenantId());
        metadata.put("employeeId", request.getEmployeeId());
        metadata.put("priority", request.getPriority());
        metadata.put("reviewFocus", request.getReviewFocus());

        if (request.getMetadata() != null) {
            metadata.putAll(request.getMetadata());
        }

        return metadata;
    }

    /**
     * 解析并结构化AI返回结果 - 使用通用解析器
     */
    private ContractReviewResponseDTO parseAndStructureResult(
        String aiResponse,
        List<ContractPartyExtractor.ContractPartyInfo> parties,
        EnterpriseInfoContext enterpriseInfo,
        LegalRegulatoryContext legalContext,
        InternalPolicyContext internalPolicy,
        Long tenantId
    ) {
        try {
            log.debug("开始解析AI响应，租户ID: {}", tenantId);

            // 使用通用解析器解析响应
            ContractReviewResponseDTO response = universalParser.parseContractReviewResponse(aiResponse);

            // 如果通用解析器成功，直接返回结果
            if (response != null && !"FAILED".equals(response.getReviewStatus())) {
                log.info("通用解析器成功解析AI响应，租户ID: {}", tenantId);
                return response;
            }

            // 如果通用解析器失败，尝试使用原有的解析逻辑作为降级方案
            log.warn("通用解析器解析失败，尝试使用原有解析逻辑，租户ID: {}", tenantId);
            return parseWithLegacyMethod(aiResponse, parties, enterpriseInfo, legalContext, internalPolicy, tenantId);

        } catch (Exception e) {
            log.error("解析AI响应失败，租户ID: {}", tenantId, e);
            return createDefaultResponse(aiResponse);
        }
    }

    /**
     * 原有的解析逻辑作为降级方案
     */
    private ContractReviewResponseDTO parseWithLegacyMethod(
        String aiResponse,
        List<ContractPartyExtractor.ContractPartyInfo> parties,
        EnterpriseInfoContext enterpriseInfo,
        LegalRegulatoryContext legalContext,
        InternalPolicyContext internalPolicy,
        Long tenantId
    ) {
        try {
            // 清理AI返回的响应，移除可能存在的markdown代码块标记
            String cleanedResponse = cleanAiResponse(aiResponse);
            log.debug("清理后的AI响应内容: {}", cleanedResponse);

            // 尝试解析JSON结果
            @SuppressWarnings("unchecked")
            Map<String, Object> resultMap = objectMapper.readValue(cleanedResponse, Map.class);
            log.debug("解析后的JSON Map: {}", resultMap);

            ContractReviewResponseDTO response = new ContractReviewResponseDTO();

            // 基本信息 - 支持中英文字段名
            String riskLevel = AiResponseParseUtil.getStringValue(resultMap, "整体风险等级评估", "overallRiskLevel");
            response.setOverallRiskLevel(parseRiskLevel(riskLevel));

            Integer riskScore = AiResponseParseUtil.getIntegerValue(resultMap, "风险评分", "riskScore");
            response.setRiskScore(riskScore);

            String riskSummary = AiResponseParseUtil.getStringValue(resultMap, "风险总结", "riskSummary");
            response.setRiskSummary(riskSummary);

            // 风险点 - 支持中英文字段名和复杂结构
            List<Object> riskPointsListRaw = AiResponseParseUtil.getListValue(resultMap, "具体风险点列表", "riskPoints");
            if (riskPointsListRaw != null && !riskPointsListRaw.isEmpty()) {
                List<ContractReviewResponseDTO.RiskPointDTO> riskPoints = new ArrayList<>();
                for (Object riskItem : riskPointsListRaw) {
                    ContractReviewResponseDTO.RiskPointDTO riskPoint = new ContractReviewResponseDTO.RiskPointDTO();

                    if (riskItem instanceof Map) {
                        // 处理复杂的风险点结构
                        @SuppressWarnings("unchecked")
                        Map<String, Object> riskMap = (Map<String, Object>) riskItem;

                        riskPoint.setCategory("合同条款风险");
                        riskPoint.setDescription(AiResponseParseUtil.getStringValue(riskMap, "风险点", "description"));

                        // 解析风险等级
                        String riskLevelStr = AiResponseParseUtil.getStringValue(riskMap, "风险等级", "riskLevel");
                        riskPoint.setSeverity(parseRiskLevel(riskLevelStr));

                        // 设置风险说明作为法律依据
                        String riskExplanation = AiResponseParseUtil.getStringValue(riskMap, "风险说明", "explanation");
                        riskPoint.setLegalBasis(riskExplanation);

                        // 根据风险等级设置分数
                        int pointRiskScore = 60; // 默认
                        if (riskPoint.getSeverity() != null) {
                            switch (riskPoint.getSeverity()) {
                                case LOW -> pointRiskScore = 30;
                                case MEDIUM -> pointRiskScore = 60;
                                case HIGH -> pointRiskScore = 90;
                            }
                        }
                        riskPoint.setRiskScore(pointRiskScore);
                        riskPoint.setIsCritical(riskPoint.getSeverity() == RiskLevel.HIGH);
                    } else {
                        // 处理简单的字符串结构
                        riskPoint.setCategory("合同条款风险");
                        String riskDescription = riskItem != null ? riskItem.toString() : "未知风险";
                        riskPoint.setDescription(riskDescription);
                        riskPoint.setSeverity(RiskLevel.MEDIUM);
                        riskPoint.setRiskScore(60);
                        riskPoint.setIsCritical(false);
                    }

                    riskPoints.add(riskPoint);
                }
                response.setRiskPoints(riskPoints);
            }

            // 如果有详细的风险点数据结构，也支持解析
            List<Map<String, Object>> detailedRiskPointsData = AiResponseParseUtil.getListMapValue(
                resultMap,
                "详细风险点",
                "detailedRiskPoints"
            );
            if (detailedRiskPointsData != null && !detailedRiskPointsData.isEmpty()) {
                List<ContractReviewResponseDTO.RiskPointDTO> riskPoints = new ArrayList<>();
                for (Map<String, Object> riskData : detailedRiskPointsData) {
                    ContractReviewResponseDTO.RiskPointDTO riskPoint = new ContractReviewResponseDTO.RiskPointDTO();
                    riskPoint.setCategory(AiResponseParseUtil.getStringValue(riskData, "类别", "category"));
                    riskPoint.setDescription(AiResponseParseUtil.getStringValue(riskData, "描述", "description"));
                    riskPoint.setSeverity(parseRiskLevel(AiResponseParseUtil.getStringValue(riskData, "严重程度", "severity")));
                    // 安全地处理列表类型
                    List<Object> affectedClausesRaw = AiResponseParseUtil.getListValue(riskData, "影响条款", "affectedClauses");
                    List<String> affectedClauses = new ArrayList<>();
                    if (affectedClausesRaw != null) {
                        for (Object clause : affectedClausesRaw) {
                            affectedClauses.add(clause != null ? clause.toString() : "");
                        }
                    }
                    riskPoint.setAffectedClauses(affectedClauses);

                    riskPoint.setLegalBasis(AiResponseParseUtil.getStringValue(riskData, "法律依据", "legalBasis"));

                    List<Object> suggestionsRaw = AiResponseParseUtil.getListValue(riskData, "建议", "suggestions");
                    List<String> suggestions = new ArrayList<>();
                    if (suggestionsRaw != null) {
                        for (Object suggestion : suggestionsRaw) {
                            suggestions.add(suggestion != null ? suggestion.toString() : "");
                        }
                    }
                    riskPoint.setSuggestions(suggestions);
                    riskPoint.setRiskScore(AiResponseParseUtil.getIntegerValue(riskData, "风险分数", "riskScore"));
                    riskPoint.setIsCritical(AiResponseParseUtil.getBooleanValue(riskData, "是否关键", "isCritical"));
                    riskPoints.add(riskPoint);
                }
                response.setRiskPoints(riskPoints);
            }

            // 关联方分析 - 支持中英文字段名
            List<Map<String, Object>> partyAnalysisData = AiResponseParseUtil.getListMapValue(resultMap, "关联方分析", "partyAnalysis");
            if (partyAnalysisData != null) {
                List<ContractReviewResponseDTO.PartyRiskAnalysisDTO> partyAnalysis = new ArrayList<>();
                for (Map<String, Object> partyData : partyAnalysisData) {
                    ContractReviewResponseDTO.PartyRiskAnalysisDTO partyRisk = new ContractReviewResponseDTO.PartyRiskAnalysisDTO();
                    partyRisk.setPartyName(AiResponseParseUtil.getStringValue(partyData, "关联方名称", "partyName"));
                    partyRisk.setPartyType(AiResponseParseUtil.getStringValue(partyData, "关联方类型", "partyType"));
                    partyRisk.setRiskLevel(parseRiskLevel(AiResponseParseUtil.getStringValue(partyData, "风险等级", "riskLevel")));

                    // 安全地处理风险因素列表
                    List<Object> riskFactorsRaw = AiResponseParseUtil.getListValue(partyData, "风险因素", "riskFactors");
                    List<String> riskFactors = new ArrayList<>();
                    if (riskFactorsRaw != null) {
                        for (Object factor : riskFactorsRaw) {
                            riskFactors.add(factor != null ? factor.toString() : "");
                        }
                    }
                    partyRisk.setRiskFactors(riskFactors);

                    // 安全地处理合规问题列表
                    List<Object> complianceIssuesRaw = AiResponseParseUtil.getListValue(partyData, "合规问题", "complianceIssues");
                    List<String> complianceIssues = new ArrayList<>();
                    if (complianceIssuesRaw != null) {
                        for (Object issue : complianceIssuesRaw) {
                            complianceIssues.add(issue != null ? issue.toString() : "");
                        }
                    }
                    partyRisk.setComplianceIssues(complianceIssues);

                    // 安全地处理建议列表
                    List<Object> recommendationsRaw = AiResponseParseUtil.getListValue(partyData, "建议", "recommendations");
                    List<String> recommendations = new ArrayList<>();
                    if (recommendationsRaw != null) {
                        for (Object rec : recommendationsRaw) {
                            recommendations.add(rec != null ? rec.toString() : "");
                        }
                    }
                    partyRisk.setRecommendations(recommendations);

                    partyRisk.setCreditRating(AiResponseParseUtil.getStringValue(partyData, "信用评级", "creditRating"));
                    partyRisk.setBusinessStatus(AiResponseParseUtil.getStringValue(partyData, "经营状态", "businessStatus"));
                    partyAnalysis.add(partyRisk);
                }
                response.setPartyAnalysis(partyAnalysis);
            }

            // 条款问题分析 - 支持中英文字段名和列表结构
            Object clauseAnalysisObj = resultMap.get("条款问题分析");
            if (clauseAnalysisObj == null) {
                clauseAnalysisObj = resultMap.get("clauseAnalysis");
            }

            if (clauseAnalysisObj != null) {
                List<ContractReviewResponseDTO.ClauseIssueDTO> clauseIssues = new ArrayList<>();

                if (clauseAnalysisObj instanceof List) {
                    // 处理列表结构的条款问题分析
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> clauseAnalysisList = (List<Map<String, Object>>) clauseAnalysisObj;

                    for (Map<String, Object> clauseData : clauseAnalysisList) {
                        ContractReviewResponseDTO.ClauseIssueDTO clauseIssue = new ContractReviewResponseDTO.ClauseIssueDTO();

                        clauseIssue.setClauseText(AiResponseParseUtil.getStringValue(clauseData, "条款", "clause"));
                        clauseIssue.setClauseNumber(AiResponseParseUtil.getStringValue(clauseData, "条款", "clause"));
                        clauseIssue.setIssueType("条款问题");
                        clauseIssue.setDescription(AiResponseParseUtil.getStringValue(clauseData, "问题", "problem"));
                        clauseIssue.setSeverity(RiskLevel.MEDIUM);
                        clauseIssue.setLegalRisk(AiResponseParseUtil.getStringValue(clauseData, "问题", "problem"));

                        // 将建议作为参考法律
                        String suggestion = AiResponseParseUtil.getStringValue(clauseData, "建议", "suggestion");
                        if (suggestion != null) {
                            clauseIssue.setReferenceLaws(List.of(suggestion));
                            clauseIssue.setSuggestions(List.of(suggestion));
                        }

                        clauseIssues.add(clauseIssue);
                    }
                } else if (clauseAnalysisObj instanceof Map) {
                    // 处理 Map 结构的条款问题分析
                    @SuppressWarnings("unchecked")
                    Map<String, Object> clauseAnalysisData = (Map<String, Object>) clauseAnalysisObj;

                    // 遍历条款问题分析中的每个条款
                    for (Map.Entry<String, Object> entry : clauseAnalysisData.entrySet()) {
                        String clauseName = entry.getKey();
                        Object clauseValue = entry.getValue();

                        if (clauseValue instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> clauseDetails = (Map<String, Object>) clauseValue;
                            ContractReviewResponseDTO.ClauseIssueDTO clauseIssue = new ContractReviewResponseDTO.ClauseIssueDTO();

                            clauseIssue.setClauseText(clauseName);
                            clauseIssue.setClauseNumber(clauseName);
                            clauseIssue.setIssueType("条款问题");
                            clauseIssue.setDescription(AiResponseParseUtil.getStringValue(clauseDetails, "问题", "problem"));
                            clauseIssue.setSeverity(RiskLevel.MEDIUM);
                            clauseIssue.setLegalRisk(AiResponseParseUtil.getStringValue(clauseDetails, "风险", "risk"));
                            clauseIssue.setReferenceLaws(
                                List.of(AiResponseParseUtil.getStringValue(clauseDetails, "法律依据", "legalBasis"))
                            );

                            clauseIssues.add(clauseIssue);
                        }
                    }
                }

                response.setClauseIssues(clauseIssues);
            }

            // 如果有标准格式的条款问题数据，也支持解析
            List<Map<String, Object>> standardClauseIssuesData = AiResponseParseUtil.getListMapValue(resultMap, "条款问题", "clauseIssues");
            if (standardClauseIssuesData != null && !standardClauseIssuesData.isEmpty()) {
                List<ContractReviewResponseDTO.ClauseIssueDTO> clauseIssues = new ArrayList<>();
                for (Map<String, Object> clauseData : standardClauseIssuesData) {
                    ContractReviewResponseDTO.ClauseIssueDTO clauseIssue = new ContractReviewResponseDTO.ClauseIssueDTO();
                    clauseIssue.setClauseText(AiResponseParseUtil.getStringValue(clauseData, "条款文本", "clauseText"));
                    clauseIssue.setClauseNumber(AiResponseParseUtil.getStringValue(clauseData, "条款编号", "clauseNumber"));
                    clauseIssue.setIssueType(AiResponseParseUtil.getStringValue(clauseData, "问题类型", "issueType"));
                    clauseIssue.setDescription(AiResponseParseUtil.getStringValue(clauseData, "描述", "description"));
                    clauseIssue.setSeverity(parseRiskLevel(AiResponseParseUtil.getStringValue(clauseData, "严重程度", "severity")));
                    clauseIssue.setLegalRisk(AiResponseParseUtil.getStringValue(clauseData, "法律风险", "legalRisk"));
                    // 安全地处理建议列表
                    List<Object> suggestionsRaw = AiResponseParseUtil.getListValue(clauseData, "建议", "suggestions");
                    List<String> suggestions = new ArrayList<>();
                    if (suggestionsRaw != null) {
                        for (Object suggestion : suggestionsRaw) {
                            suggestions.add(suggestion != null ? suggestion.toString() : "");
                        }
                    }
                    clauseIssue.setSuggestions(suggestions);

                    // 安全地处理参考法律列表
                    List<Object> referenceLawsRaw = AiResponseParseUtil.getListValue(clauseData, "参考法律", "referenceLaws");
                    List<String> referenceLaws = new ArrayList<>();
                    if (referenceLawsRaw != null) {
                        for (Object law : referenceLawsRaw) {
                            referenceLaws.add(law != null ? law.toString() : "");
                        }
                    }
                    clauseIssue.setReferenceLaws(referenceLaws);
                    clauseIssues.add(clauseIssue);
                }
                response.setClauseIssues(clauseIssues);
            }

            // 合规检查结果 - 支持中英文字段名
            Map<String, Object> complianceData = AiResponseParseUtil.getMapValue(resultMap, "合规检查结果", "complianceCheck");
            if (complianceData != null) {
                ContractReviewResponseDTO.ComplianceCheckResultDTO complianceCheck =
                    new ContractReviewResponseDTO.ComplianceCheckResultDTO();

                // 安全地处理违规法规列表
                List<Object> violatedRegulationsRaw = AiResponseParseUtil.getListValue(complianceData, "违规法规", "violatedRegulations");
                List<String> violatedRegulations = new ArrayList<>();
                if (violatedRegulationsRaw != null) {
                    for (Object regulation : violatedRegulationsRaw) {
                        violatedRegulations.add(regulation != null ? regulation.toString() : "");
                    }
                }
                complianceCheck.setViolatedRegulations(violatedRegulations);

                // 安全地处理内部制度违规列表
                List<Object> internalPolicyViolationsRaw = AiResponseParseUtil.getListValue(
                    complianceData,
                    "内部制度违规",
                    "internalPolicyViolations"
                );
                List<String> internalPolicyViolations = new ArrayList<>();
                if (internalPolicyViolationsRaw != null) {
                    for (Object violation : internalPolicyViolationsRaw) {
                        internalPolicyViolations.add(violation != null ? violation.toString() : "");
                    }
                }
                complianceCheck.setInternalPolicyViolations(internalPolicyViolations);

                // 安全地处理建议列表
                List<Object> recommendationsRaw = AiResponseParseUtil.getListValue(complianceData, "建议", "recommendations");
                List<String> recommendations = new ArrayList<>();
                if (recommendationsRaw != null) {
                    for (Object rec : recommendationsRaw) {
                        recommendations.add(rec != null ? rec.toString() : "");
                    }
                }
                complianceCheck.setRecommendations(recommendations);

                // 安全地处理必要行动列表
                List<Object> requiredActionsRaw = AiResponseParseUtil.getListValue(complianceData, "必要行动", "requiredActions");
                List<String> requiredActions = new ArrayList<>();
                if (requiredActionsRaw != null) {
                    for (Object action : requiredActionsRaw) {
                        requiredActions.add(action != null ? action.toString() : "");
                    }
                }
                complianceCheck.setRequiredActions(requiredActions);

                complianceCheck.setOverallCompliance(
                    AiResponseParseUtil.getBooleanValue(complianceData, "整体合规性", "overallCompliance")
                );
                complianceCheck.setComplianceScore(AiResponseParseUtil.getStringValue(complianceData, "合规分数", "complianceScore"));
                response.setComplianceCheck(complianceCheck);
            }

            // 修改建议 - 支持中英文字段名
            List<String> recommendations = new ArrayList<>();

            // 处理修改建议列表
            List<Object> modificationSuggestions = AiResponseParseUtil.getListValue(resultMap, "修改建议", "recommendations");
            if (modificationSuggestions != null) {
                for (Object suggestion : modificationSuggestions) {
                    try {
                        if (suggestion instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> suggestionMap = (Map<String, Object>) suggestion;
                            String clause = AiResponseParseUtil.getStringValue(suggestionMap, "条款", "clause");
                            String advice = AiResponseParseUtil.getStringValue(suggestionMap, "建议", "suggestion");
                            if (clause != null && advice != null) {
                                recommendations.add(clause + ": " + advice);
                            } else if (advice != null) {
                                recommendations.add(advice);
                            } else {
                                recommendations.add(suggestion.toString());
                            }
                        } else if (suggestion instanceof String) {
                            recommendations.add((String) suggestion);
                        } else {
                            // 对于其他类型，安全地转换为字符串
                            recommendations.add(suggestion.toString());
                        }
                    } catch (Exception e) {
                        log.warn("解析修改建议时出错: {}", suggestion, e);
                        recommendations.add("解析失败的建议: " + suggestion);
                    }
                }
            }

            // 如果没有修改建议，尝试获取标准建议
            if (recommendations.isEmpty()) {
                List<Object> standardRecommendationsRaw = AiResponseParseUtil.getListValue(resultMap, "建议", "recommendations");
                if (standardRecommendationsRaw != null) {
                    for (Object rec : standardRecommendationsRaw) {
                        recommendations.add(rec != null ? rec.toString() : "");
                    }
                }
            }

            response.setRecommendations(recommendations);

            // 下一步行动
            List<Object> nextActionsRaw = AiResponseParseUtil.getListValue(resultMap, "下一步行动", "nextActions");
            List<String> nextActions = new ArrayList<>();
            if (nextActionsRaw != null) {
                for (Object action : nextActionsRaw) {
                    nextActions.add(action != null ? action.toString() : "");
                }
            }
            response.setNextActions(nextActions);

            // 设置其他信息
            response.setReviewStatus("COMPLETED");
            response.setAiModelInfo("contract-review-comprehensive");
            response.setConfidence(calculateConfidence(response));

            return response;
        } catch (Exception e) {
            log.error("解析AI返回结果失败，租户: {}", tenantId, e);
            // 返回默认结果
            return createDefaultResponse(aiResponse);
        }
    }

    /**
     * 清理AI返回的响应，移除markdown代码块标记
     *
     * @param aiResponse 原始AI响应
     * @return 清理后的JSON字符串
     */
    private String cleanAiResponse(String aiResponse) {
        if (aiResponse == null) {
            return null;
        }

        log.debug("原始AI响应: {}", aiResponse);

        String cleaned = aiResponse;

        // 移除各种可能的markdown代码块标记
        cleaned = cleaned.replaceAll("```json\\s*", "");
        cleaned = cleaned.replaceAll("```JSON\\s*", "");
        cleaned = cleaned.replaceAll("```\\s*", "");

        // 移除可能存在的前导文本（如"以下是对合同的智能审查分析结果："）
        int jsonStart = cleaned.indexOf("{");
        if (jsonStart > 0) {
            cleaned = cleaned.substring(jsonStart);
        }

        // 移除可能存在的尾随文本
        int jsonEnd = cleaned.lastIndexOf("}");
        if (jsonEnd > 0 && jsonEnd < cleaned.length() - 1) {
            cleaned = cleaned.substring(0, jsonEnd + 1);
        }

        // 移除可能存在的前导和尾随空白字符
        cleaned = cleaned.trim();

        log.debug("清理后的AI响应: {}", cleaned);
        return cleaned;
    }

    /**
     * 解析风险等级 - 支持中英文
     */
    private RiskLevel parseRiskLevel(String riskLevelStr) {
        if (riskLevelStr == null) {
            return RiskLevel.MEDIUM;
        }

        // 先尝试直接解析英文枚举值
        try {
            return RiskLevel.valueOf(riskLevelStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 如果失败，尝试中文映射
            String normalizedStr = riskLevelStr.trim();
            switch (normalizedStr) {
                case "低":
                case "低风险":
                case "低等":
                    return RiskLevel.LOW;
                case "中":
                case "中等":
                case "中风险":
                case "中等风险":
                    return RiskLevel.MEDIUM;
                case "高":
                case "高风险":
                case "高等":
                    return RiskLevel.HIGH;
                default:
                    log.warn("无法解析风险等级: {}，使用默认值 MEDIUM", riskLevelStr);
                    return RiskLevel.MEDIUM;
            }
        }
    }

    /**
     * 计算置信度
     */
    private Integer calculateConfidence(ContractReviewResponseDTO response) {
        int confidence = 80; // 基础置信度

        // 根据风险点数量调整
        if (response.getRiskPoints() != null) {
            confidence += Math.min(response.getRiskPoints().size() * 2, 10);
        }

        // 根据关联方分析调整
        if (response.getPartyAnalysis() != null) {
            confidence += Math.min(response.getPartyAnalysis().size() * 3, 10);
        }

        return Math.min(confidence, 95);
    }

    /**
     * 创建默认响应（解析失败时使用）
     */
    private ContractReviewResponseDTO createDefaultResponse(String aiResponse) {
        ContractReviewResponseDTO response = new ContractReviewResponseDTO();
        response.setOverallRiskLevel(RiskLevel.MEDIUM);
        response.setRiskScore(50);
        response.setRiskSummary("AI分析结果解析失败，请人工审查");
        response.setReviewStatus("FAILED");
        response.setConfidence(30);

        // 将原始AI响应作为建议
        List<String> recommendations = new ArrayList<>();
        recommendations.add("AI原始响应：" + aiResponse);
        response.setRecommendations(recommendations);

        return response;
    }

    /**
     * 更新审查记录
     */
    private void updateReviewRecord(
        ContractReview reviewRecord,
        ContractReviewResponseDTO response,
        AiRequestDTO aiResponse,
        long duration
    ) {
        try {
            reviewRecord.setStatus(ReviewStatus.COMPLETED);
            reviewRecord.setOverallRiskLevel(response.getOverallRiskLevel());
            reviewRecord.setRiskScore(response.getRiskScore());
            reviewRecord.setRiskSummary(response.getRiskSummary());
            reviewRecord.setReviewResult(objectMapper.writeValueAsString(response));
            reviewRecord.setReviewEndTime(Instant.now());
            reviewRecord.setReviewDuration(duration);
            reviewRecord.setAiRequestId(aiResponse.getId());
            reviewRecord.setUpdatedAt(Instant.now());
            reviewRecord.setUpdatedBy(reviewRecord.getEmployeeId().toString());

            reviewRepository.save(reviewRecord);
        } catch (Exception e) {
            log.error("更新审查记录失败，记录ID: {}", reviewRecord.getId(), e);
        }
    }

    /**
     * 更新审查记录（错误情况）
     */
    private void updateReviewRecordWithError(ContractReview reviewRecord, Exception error) {
        try {
            reviewRecord.setStatus(ReviewStatus.FAILED);
            reviewRecord.setReviewEndTime(Instant.now());
            reviewRecord.setReviewDuration(System.currentTimeMillis() - reviewRecord.getReviewStartTime().toEpochMilli());

            // 记录错误信息
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", error.getMessage());
            errorInfo.put("errorType", error.getClass().getSimpleName());
            errorInfo.put("timestamp", Instant.now().toString());

            reviewRecord.setReviewResult(objectMapper.writeValueAsString(errorInfo));
            reviewRecord.setUpdatedAt(Instant.now());
            reviewRecord.setUpdatedBy(reviewRecord.getEmployeeId().toString());

            reviewRepository.save(reviewRecord);
        } catch (Exception e) {
            log.error("更新错误审查记录失败，记录ID: {}", reviewRecord.getId(), e);
        }
    }

    /**
     * 获取审查历史（分页）
     */
    public Page<ContractReviewController.ContractReviewHistoryDTO> getReviewHistory(Long tenantId, Long employeeId, Pageable pageable) {
        log.debug("查询审查历史 - 租户: {}, 员工: {}", tenantId, employeeId);

        // 这里需要实现具体的分页查询逻辑
        // 由于涉及到Repository的分页查询，暂时返回空页面
        return new PageImpl<>(new ArrayList<>(), pageable, 0);
    }

    /**
     * 获取审查详情
     */
    public ContractReviewResponseDTO getReviewDetail(Long reviewId, Long tenantId) {
        log.debug("获取审查详情 - 审查ID: {}, 租户: {}", reviewId, tenantId);

        // 这里需要实现具体的查询逻辑
        // 暂时返回null，需要后续实现
        return null;
    }

    /**
     * 导出审查报告
     */
    public byte[] exportReviewReport(Long reviewId, Long tenantId, String format) {
        log.info("导出审查报告 - 审查ID: {}, 租户: {}, 格式: {}", reviewId, tenantId, format);

        // 这里需要实现具体的报告导出逻辑
        // 暂时返回空数组，需要后续实现
        return new byte[0];
    }

    /**
     * 获取审查统计信息
     */
    public Map<String, Object> getReviewStatistics(Long tenantId, Integer days) {
        log.debug("获取审查统计信息 - 租户: {}, 天数: {}", tenantId, days);

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalReviews", 0);
        statistics.put("highRiskCount", 0);
        statistics.put("mediumRiskCount", 0);
        statistics.put("lowRiskCount", 0);
        statistics.put("averageRiskScore", 0);
        statistics.put("averageReviewTime", 0);

        // 这里需要实现具体的统计逻辑
        // 暂时返回默认值，需要后续实现
        return statistics;
    }
}
