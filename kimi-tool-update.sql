-- 更新 Kimi 工具配置的 SQL 语句
-- 修复认证问题，确保 API Key 正确设置

-- 方案1: 如果记录还没有插入，先删除可能的重复记录
DELETE FROM ai_tool WHERE tool_key = 'kimi';

-- 然后重新插入正确的配置
INSERT INTO ai_tool (
    tenant_id,
    name,
    tool_key,
    version,
    api_url,
    api_key,
    auth_type,
    path,
    status,
    weight,
    max_concurrent_calls,
    is_model,
    model_category,
    model_provider,
    remark,
    metadata,
    created_by,
    created_at,
    updated_by,
    updated_at,
    is_deleted
) VALUES (
    1,                                                              -- tenant_id
    'Kimi AI模型',                                                   -- name
    'kimi',                                                         -- tool_key
    1,                                                              -- version
    'https://ark.cn-beijing.volces.com/api/v3',                   -- api_url: API基础地址
    'sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ',        -- api_key: 您的API密钥
    'Bearer',                                                       -- auth_type
    '/chat/completions',                                           -- path
    'AVAILABLE',                                                   -- status
    100,                                                           -- weight
    5,                                                             -- max_concurrent_calls
    true,                                                          -- is_model
    'TEXT_GENERATION',                                             -- model_category
    'ByteDance',                                                   -- model_provider
    'Kimi AI 基于字节跳动火山引擎豆包API，模型：doubao-seed-1-6-250615', -- remark
    '{"model":"doubao-seed-1-6-250615","temperature":0.7,"max_tokens":2048,"supports_images":true}', -- metadata
    'system',                                                      -- created_by
    NOW(),                                                         -- created_at
    'system',                                                      -- updated_by
    NOW(),                                                         -- updated_at
    false                                                          -- is_deleted
);

-- 方案2: 如果记录已经存在，只需要更新配置
-- UPDATE ai_tool SET 
--     api_url = 'https://ark.cn-beijing.volces.com/api/v3',
--     api_key = 'sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ',
--     path = '/chat/completions',
--     metadata = '{"model":"doubao-seed-1-6-250615","temperature":0.7,"max_tokens":2048,"supports_images":true}',
--     updated_at = NOW()
-- WHERE tool_key = 'kimi' AND is_deleted = false;

-- 验证配置
SELECT 
    id,
    name,
    tool_key,
    api_url,
    LEFT(api_key, 10) || '...' as api_key_preview,
    path,
    status,
    metadata
FROM ai_tool 
WHERE tool_key = 'kimi' 
  AND is_deleted = false;
