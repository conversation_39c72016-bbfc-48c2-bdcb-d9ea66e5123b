-- =====================================================
-- Kimi API Key 更新脚本
-- =====================================================
-- 
-- 此脚本用于更新 Kimi API 的配置，解决 401 Unauthorized 错误
-- 
-- 使用前请确保：
-- 1. 已从火山引擎控制台获取有效的 API Key
-- 2. 已开通 doubao-seed-1-6-250615 模型服务
-- 3. 备份当前数据库配置
-- 
-- 执行方式：
-- docker exec -it whiskerguardaiservice-mysql-1 mysql -u root < kimi-api-key-update.sql
-- 
-- =====================================================

USE whiskerguardaiservice;

-- 1. 查看当前配置
SELECT 
    '=== 当前 Kimi API 配置 ===' as info,
    tool_key, 
    api_key, 
    api_url, 
    path, 
    status,
    remark,
    updated_at
FROM ai_tool 
WHERE tool_key = 'kimi' AND is_deleted = false;

-- 2. 备份当前配置（可选）
-- CREATE TABLE ai_tool_backup_kimi AS 
-- SELECT * FROM ai_tool WHERE tool_key = 'kimi' AND is_deleted = false;

-- 3. 更新 API Key 配置
-- 
-- ⚠️ 重要提醒：请将下面的 'YOUR_ACTUAL_VOLCANO_ENGINE_API_KEY' 
-- 替换为您从火山引擎控制台获取的真实 API Key
-- 
-- API Key 获取步骤：
-- 1. 登录 https://console.volcengine.com/ark
-- 2. 进入 "API Key 管理" 页面
-- 3. 创建新的 API Key 或使用现有的
-- 4. 复制完整的 API Key（包括前缀）
-- 
UPDATE ai_tool SET 
    api_key = 'YOUR_ACTUAL_VOLCANO_ENGINE_API_KEY',  -- ⚠️ 请替换为真实的 API Key
    updated_at = NOW(),
    updated_by = 'admin',
    remark = '火山引擎豆包 API - 已配置真实 API Key'
WHERE tool_key = 'kimi' 
  AND is_deleted = false;

-- 4. 验证更新结果
SELECT 
    '=== 更新后的 Kimi API 配置 ===' as info,
    tool_key, 
    CONCAT(LEFT(api_key, 10), '***') as api_key_masked,  -- 脱敏显示
    api_url, 
    path, 
    status,
    remark,
    updated_at
FROM ai_tool 
WHERE tool_key = 'kimi' AND is_deleted = false;

-- 5. 检查配置完整性
SELECT 
    CASE 
        WHEN api_key IS NULL OR api_key = '' THEN '❌ API Key 为空'
        WHEN api_key = 'YOUR_ACTUAL_VOLCANO_ENGINE_API_KEY' THEN '❌ API Key 未更新，仍为占位符'
        WHEN api_key = 'sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ' THEN '❌ API Key 仍为测试密钥'
        WHEN LENGTH(api_key) < 10 THEN '❌ API Key 长度过短'
        ELSE '✅ API Key 配置正常'
    END as api_key_status,
    
    CASE 
        WHEN api_url IS NULL OR api_url = '' THEN '❌ API URL 为空'
        WHEN api_url != 'https://ark.cn-beijing.volces.com/api/v3' THEN '⚠️ API URL 可能不正确'
        ELSE '✅ API URL 配置正常'
    END as api_url_status,
    
    CASE 
        WHEN path IS NULL OR path = '' THEN '❌ API Path 为空'
        WHEN path != '/chat/completions' THEN '⚠️ API Path 可能不正确'
        ELSE '✅ API Path 配置正常'
    END as api_path_status,
    
    CASE 
        WHEN status != 'AVAILABLE' THEN '❌ 工具状态不可用'
        ELSE '✅ 工具状态正常'
    END as tool_status
FROM ai_tool 
WHERE tool_key = 'kimi' AND is_deleted = false;

-- 6. 显示配置指南
SELECT '
=====================================================
📋 配置完成后的验证步骤：

1. 重启应用服务：
   docker-compose restart whiskerguardaiservice

2. 查看应用日志：
   docker logs whiskerguardaiservice-app-1 -f

3. 测试 API 调用：
   发送 POST 请求到 /api/ai/invoke 端点

4. 如果仍有问题，请查看：
   - docs/kimi-api-configuration-guide.md
   - 火山引擎控制台的 API Key 状态
   - 账号余额和权限设置

5. 常见错误解决：
   - 401 Unauthorized: API Key 无效或未开通服务
   - 403 Forbidden: 权限不足或余额不足
   - 超时错误: 网络连接问题

📞 如需帮助，请参考项目文档或联系技术支持
=====================================================
' as configuration_guide;

-- 7. 可选：如果需要回滚配置
-- UPDATE ai_tool SET 
--     api_key = 'sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ',
--     updated_at = NOW(),
--     remark = '已回滚到测试配置'
-- WHERE tool_key = 'kimi' AND is_deleted = false;
