#!/bin/bash

# =====================================================
# Kimi API Key 验证脚本
# =====================================================
# 
# 此脚本用于验证火山引擎 API Key 是否有效
# 
# 使用方式：
# chmod +x scripts/validate-kimi-key.sh
# ./scripts/validate-kimi-key.sh YOUR_API_KEY
# 
# =====================================================

if [ $# -eq 0 ]; then
    echo "❌ 请提供 API Key 作为参数"
    echo "使用方式: $0 YOUR_API_KEY"
    echo ""
    echo "示例: $0 sk-your-actual-api-key-here"
    exit 1
fi

API_KEY="$1"

echo "🔍 验证 Kimi API Key..."
echo "=================================="
echo "API Key 前缀: $(echo "$API_KEY" | head -c 10)***"
echo ""

# 基本格式检查
echo "📋 1. 基本格式检查..."

if [ ${#API_KEY} -lt 10 ]; then
    echo "❌ API Key 长度过短 (${#API_KEY} 字符)"
    exit 1
fi

# 检查是否是已知的测试 Key
if [ "$API_KEY" = "sk-qVFZubkkt2AHnYOAmcENhjrVndhiL5zQRi3hVZ8BvlWjPxmQ" ]; then
    echo "❌ 这是测试用的占位符 API Key，不是真实的密钥"
    echo "   请从火山引擎控制台获取真实的 API Key"
    exit 1
fi

if echo "$API_KEY" | grep -qi "test\|example\|placeholder\|demo"; then
    echo "⚠️  API Key 包含测试相关字样，请确认这是真实的密钥"
fi

echo "✅ 基本格式检查通过"

# 测试 API 调用
echo ""
echo "📋 2. 测试 API 调用..."

RESPONSE=$(curl -s -w "\n%{http_code}" \
    -X POST https://ark.cn-beijing.volces.com/api/v3/chat/completions \
    -H "Authorization: Bearer $API_KEY" \
    -H "Content-Type: application/json" \
    -d '{
        "model": "doubao-seed-1-6-250615",
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 10
    }' \
    --connect-timeout 10 \
    --max-time 30 2>/dev/null)

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP 状态码: $HTTP_CODE"

case $HTTP_CODE in
    200)
        echo "✅ API Key 验证成功！"
        echo "   API 调用正常，可以使用此 Key"
        
        # 尝试解析响应
        if echo "$RESPONSE_BODY" | grep -q '"choices"'; then
            echo "   响应格式正确"
        else
            echo "   响应格式异常，请检查"
        fi
        ;;
    401)
        echo "❌ API Key 认证失败 (401 Unauthorized)"
        echo "   可能的原因："
        echo "   - API Key 无效或已过期"
        echo "   - 未开通对应的模型服务"
        echo "   - API Key 格式不正确"
        
        if echo "$RESPONSE_BODY" | grep -q "AuthenticationError"; then
            echo "   错误详情: $(echo "$RESPONSE_BODY" | grep -o '"message":"[^"]*"' | sed 's/"message":"//;s/"$//')"
        fi
        exit 1
        ;;
    403)
        echo "❌ 权限不足 (403 Forbidden)"
        echo "   可能的原因："
        echo "   - API Key 权限不足"
        echo "   - 账号余额不足"
        echo "   - 请求频率超限"
        exit 1
        ;;
    429)
        echo "⚠️  请求频率超限 (429 Too Many Requests)"
        echo "   请稍后再试"
        exit 1
        ;;
    500|502|503)
        echo "⚠️  服务器错误 ($HTTP_CODE)"
        echo "   火山引擎服务可能暂时不可用，请稍后再试"
        exit 1
        ;;
    000)
        echo "❌ 网络连接失败"
        echo "   请检查网络连接和防火墙设置"
        exit 1
        ;;
    *)
        echo "❌ 未知错误 (HTTP $HTTP_CODE)"
        echo "   响应内容: $RESPONSE_BODY"
        exit 1
        ;;
esac

echo ""
echo "=================================="
echo "🎉 验证完成！"
echo ""
echo "📋 下一步操作："
echo "1. 更新数据库配置:"
echo "   docker exec -it whiskerguardaiservice-mysql-1 mysql -u root -e \\"
echo "   \"USE whiskerguardaiservice; UPDATE ai_tool SET api_key='$API_KEY', updated_at=NOW() WHERE tool_key='kimi';\""
echo ""
echo "2. 重启应用服务"
echo ""
echo "3. 测试应用 API 调用"
