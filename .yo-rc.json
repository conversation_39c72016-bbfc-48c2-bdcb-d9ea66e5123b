{"generator-jhipster": {"applicationType": "microservice", "authenticationType": "jwt", "baseName": "whiskerguardAiService", "buildTool": "maven", "cacheProvider": "redis", "clientFramework": "no", "clientTestFrameworks": null, "clientTheme": null, "creationTimestamp": 1746532279606, "databaseType": "sql", "devDatabaseType": "mysql", "enableHibernateCache": true, "enableTranslation": true, "entities": ["AiTool", "AiRequest", "AiReview", "AiToolMetrics", "ContractReview", "ContractParty", "ContractRiskPoint", "ContractClauseIssue", "PromptTemplate", "PromptTemplateVariable", "PromptTemplateVersion", "TenantPromptConfig"], "feignClient": true, "jhipsterVersion": "8.10.0", "jwtSecretKey": "YzU3OGViYWI3NzBjNzAxMTM1MDNhNzczZTZmZTJiNzk1Y2ZkMzUxNWM0ODIyMzA5NmU3MjA0M2RjNjUwYTAxYTRhYzFkMzg4YTFmOTVjYzJlZTY0YWY3ZjEzNjQ0NWQ3ODU4NzczZTY1MzY0MjQzM2NiNDc2NDMzNWRiNDk4NWE=", "languages": ["zh-cn"], "lastLiquibaseTimestamp": 1750066873000, "microfrontend": null, "microfrontends": [], "nativeLanguage": "zh-cn", "packageName": "com.whiskerguard.ai", "prodDatabaseType": "mysql", "reactive": false, "serverPort": "8085", "serviceDiscoveryType": "consul", "skipClient": true, "skipUserManagement": true, "syncUserWithIdp": null, "testFrameworks": [], "withAdminUi": null}}