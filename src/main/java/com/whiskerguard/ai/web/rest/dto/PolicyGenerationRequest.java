package com.whiskerguard.ai.web.rest.dto;

import com.whiskerguard.ai.service.compliance.CompanyInfoDTO;
import java.io.Serializable;

/**
 * 政策生成请求DTO
 * 包含法规文本、企业信息等用于生成企业内部管理制度的数据
 */
public class PolicyGenerationRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private String legalText; // 法规文本
    private CompanyInfoDTO companyInfo; // 企业信息
    private Long tenantId; // 租户ID
    /** 员工ID，用于审计和追踪 */
    private Long employeeId; // 员工ID，用于审计和追踪
    private String[] modelNames; // 要使用的模型名称列表，如 ["gpt", "deepseek", "kimi"]
    private Boolean isAsync; // 是否异步处理，true为异步，false为同步

    /**
     * 集成策略
     * ARBITRATION - 仲裁模式：使用强模型整合其他模型结果
     * VOTING - 投票算法：基于多数共识生成最终结果
     * COMPLEMENTARY - 互补整合：利用不同模型的优势领域
     */
    private String integrationStrategy;

    /** 指定的AI模型 - 可选，默认使用kimi。如果同时指定了modelNames和aiModel，优先使用aiModel */
    private String aiModel;

    public String getLegalText() {
        return legalText;
    }

    public void setLegalText(String legalText) {
        this.legalText = legalText;
    }

    public CompanyInfoDTO getCompanyInfo() {
        return companyInfo;
    }

    public void setCompanyInfo(CompanyInfoDTO companyInfo) {
        this.companyInfo = companyInfo;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String[] getModelNames() {
        return modelNames;
    }

    public void setModelNames(String[] modelNames) {
        this.modelNames = modelNames;
    }

    public Boolean getIsAsync() {
        return isAsync;
    }

    public void setIsAsync(Boolean isAsync) {
        this.isAsync = isAsync;
    }

    public String getIntegrationStrategy() {
        return integrationStrategy;
    }

    public void setIntegrationStrategy(String integrationStrategy) {
        this.integrationStrategy = integrationStrategy;
    }

    public String getAiModel() {
        return aiModel;
    }

    public void setAiModel(String aiModel) {
        this.aiModel = aiModel;
    }
}
